"""
设备管理页面
"""

import streamlit as st
import pandas as pd
import plotly.express as px
from datetime import datetime, timedelta
import time

from config.settings import AppConfig
from config.theme import apply_plotly_theme
from utils.auth import check_authentication, require_permission
from utils.api_client import APIClient

# 页面配置
st.set_page_config(
    page_title="设备管理 - Smart APS",
    page_icon="🏭",
    layout="wide"
)

# 检查认证
if not check_authentication():
    st.error("请先登录")
    st.stop()

# 检查权限
if not require_permission("equipment.view"):
    st.error("权限不足")
    st.stop()

# 初始化API客户端
if 'api_client' not in st.session_state:
    st.session_state.api_client = APIClient(AppConfig.API_BASE_URL)

api_client = st.session_state.api_client

# 页面标题
st.title("🏭 设备管理")
st.markdown("### 管理生产设备信息、监控设备状态、安排维护计划")

# 侧边栏 - 筛选和操作
with st.sidebar:
    st.markdown("### 🔍 筛选条件")

    # 设备类型筛选
    equipment_types = ["全部", "生产线", "储罐设备", "加工中心", "数控车床", "铣床", "钻床", "磨床", "装配线"]
    selected_type = st.selectbox("设备类型", equipment_types)

    # 状态筛选
    status_options = ["全部", "正常运行", "可用待机", "忙碌生产", "停线", "维护中", "离线", "故障", "待机"]
    selected_status = st.selectbox("设备状态", status_options)

    # 车间/区域筛选
    workshops = ["全部", "生产线区域", "储罐区域", "车间A", "车间B", "车间C", "装配车间"]
    selected_workshop = st.selectbox("所属区域", workshops)

    # 设备编号筛选
    equipment_series = ["全部", "L系列(生产线)", "Tank系列(储罐)", "CNC系列", "其他"]
    selected_series = st.selectbox("设备系列", equipment_series)

    # 搜索
    search_keyword = st.text_input("搜索设备", placeholder="输入设备编码或名称...")

    st.markdown("---")

    # 快速操作
    st.markdown("### ⚡ 快速操作")

    if st.button("🔄 刷新数据", use_container_width=True):
        st.rerun()

    if require_permission("equipment.create"):
        if st.button("➕ 添加设备", use_container_width=True):
            st.session_state.show_create_modal = True
            st.rerun()

        # 快速添加常用设备类型
        st.markdown("##### 🚀 快速添加")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("🏭 添加生产线", use_container_width=True, help="快速添加L系列生产线"):
                st.session_state.quick_add_type = "生产线"
                st.session_state.show_quick_add_modal = True
                st.rerun()

        with col2:
            if st.button("🛢️ 添加储罐", use_container_width=True, help="快速添加Tank系列储罐"):
                st.session_state.quick_add_type = "储罐设备"
                st.session_state.show_quick_add_modal = True
                st.rerun()

    if st.button("📊 导出报表", use_container_width=True):
        export_equipment_report()

# 主要内容区域
tab1, tab2, tab3, tab4 = st.tabs(["📋 设备列表", "📊 状态监控", "🔧 维护管理", "📈 统计分析"])

with tab1:
    st.markdown("#### 📋 设备列表")

    # 获取设备列表
    equipment_data = get_equipment_list(
        equipment_type=selected_type if selected_type != "全部" else None,
        status_filter=selected_status if selected_status != "全部" else None,
        workshop=selected_workshop if selected_workshop != "全部" else None,
        search=search_keyword if search_keyword else None
    )

    if equipment_data and equipment_data.get("success"):
        equipment_list = equipment_data.get("equipment", [])
        total_count = equipment_data.get("total", 0)

        # 显示统计信息
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("设备总数", total_count)

        with col2:
            available_count = sum(1 for eq in equipment_list if eq.get("status") == "available")
            st.metric("可用设备", available_count, delta=f"{available_count/total_count*100:.1f}%" if total_count > 0 else "0%")

        with col3:
            busy_count = sum(1 for eq in equipment_list if eq.get("status") == "busy")
            st.metric("忙碌设备", busy_count)

        with col4:
            maintenance_count = sum(1 for eq in equipment_list if eq.get("status") == "maintenance")
            st.metric("维护中", maintenance_count, delta_color="inverse" if maintenance_count > 0 else "normal")

        st.markdown("---")

        # 设备列表表格
        if equipment_list:
            # 转换为DataFrame便于显示
            df_equipment = pd.DataFrame(equipment_list)

            # 自定义列显示
            display_columns = {
                "equipment_code": "设备编码",
                "equipment_name": "设备名称",
                "equipment_type": "设备类型",
                "status": "状态",
                "workshop": "车间",
                "utilization_rate": "利用率",
                "oee": "OEE"
            }

            # 格式化数据
            for idx, row in df_equipment.iterrows():
                col1, col2, col3, col4, col5, col6, col7 = st.columns([1.5, 2, 1.5, 1, 1, 1, 1.5])

                with col1:
                    st.write(f"**{row.get('equipment_code', 'N/A')}**")

                with col2:
                    st.write(row.get('equipment_name', 'N/A'))

                with col3:
                    st.write(row.get('equipment_type', 'N/A'))

                with col4:
                    status = row.get('status', 'unknown')
                    status_color = get_status_color(status)
                    status_text = get_status_text(status)
                    st.markdown(f"<span style='color: {status_color}'>●</span> {status_text}",
                              unsafe_allow_html=True)

                with col5:
                    st.write(row.get('workshop', 'N/A'))

                with col6:
                    utilization = row.get('utilization_rate', 0)
                    if utilization:
                        st.metric("", f"{utilization:.1f}%", delta=None)
                    else:
                        st.write("N/A")

                with col7:
                    # 操作按钮
                    col_view, col_edit, col_status = st.columns(3)

                    with col_view:
                        if st.button("👁️", key=f"view_{row.get('id')}", help="查看详情"):
                            show_equipment_detail(row.get('id'))

                    with col_edit:
                        if require_permission("equipment.update"):
                            if st.button("✏️", key=f"edit_{row.get('id')}", help="编辑设备"):
                                st.session_state.edit_equipment_id = row.get('id')
                                st.session_state.show_edit_modal = True
                                st.rerun()

                    with col_status:
                        if require_permission("equipment.update"):
                            if st.button("🔄", key=f"status_{row.get('id')}", help="快速切换状态"):
                                st.session_state.change_status_equipment_id = row.get('id')
                                st.session_state.show_status_modal = True
                                st.rerun()

                st.markdown("---")
        else:
            st.info("暂无设备数据")

    else:
        st.error("获取设备列表失败")

with tab2:
    st.markdown("#### 📊 设备状态监控")

    # 获取设备状态概览
    status_overview = get_equipment_status_overview()

    if status_overview and status_overview.get("success"):
        overview_data = status_overview.get("data", {})

        # 状态分布图表
        col1, col2 = st.columns(2)

        with col1:
            # 设备状态饼图
            status_counts = overview_data.get("status_distribution", {})
            if status_counts:
                fig_status = px.pie(
                    values=list(status_counts.values()),
                    names=[get_status_text(k) for k in status_counts.keys()],
                    title="📊 设备状态分布",
                    color_discrete_map={
                        "可用": "#2E8B57",
                        "忙碌": "#FFD700",
                        "维护中": "#FF6347",
                        "离线": "#808080"
                    }
                )
                fig_status = apply_plotly_theme(fig_status)
                st.plotly_chart(fig_status, use_container_width=True)

        with col2:
            # 设备类型分布
            type_counts = overview_data.get("type_distribution", {})
            if type_counts:
                fig_types = px.bar(
                    x=list(type_counts.keys()),
                    y=list(type_counts.values()),
                    title="📈 设备类型分布",
                    color=list(type_counts.values()),
                    color_continuous_scale="viridis"
                )
                fig_types = apply_plotly_theme(fig_types)
                st.plotly_chart(fig_types, use_container_width=True)

        # 实时状态监控
        st.markdown("##### 🔴 实时状态监控")

        # 模拟实时数据更新
        if st.button("🔄 刷新实时数据"):
            st.rerun()

        # 显示关键设备状态
        critical_equipment = overview_data.get("critical_equipment", [])
        if critical_equipment:
            for equipment in critical_equipment:
                with st.container():
                    col1, col2, col3, col4 = st.columns([2, 1, 1, 1])

                    with col1:
                        st.write(f"**{equipment.get('name')}** ({equipment.get('code')})")

                    with col2:
                        status = equipment.get('status')
                        status_color = get_status_color(status)
                        st.markdown(f"<span style='color: {status_color}'>●</span> {get_status_text(status)}",
                                  unsafe_allow_html=True)

                    with col3:
                        utilization = equipment.get('utilization', 0)
                        st.metric("利用率", f"{utilization:.1f}%")

                    with col4:
                        oee = equipment.get('oee', 0)
                        st.metric("OEE", f"{oee:.1f}%")

                    st.markdown("---")

    else:
        st.error("获取设备状态概览失败")

with tab3:
    st.markdown("#### 🔧 维护管理")

    # 维护计划概览
    col1, col2 = st.columns([2, 1])

    with col1:
        st.markdown("##### 📅 维护计划")

        # 获取维护计划数据
        maintenance_plans = get_maintenance_plans()

        if maintenance_plans:
            for plan in maintenance_plans:
                with st.expander(f"🔧 {plan['equipment_name']} - {plan['maintenance_type']}", expanded=False):
                    col1, col2, col3 = st.columns(3)

                    with col1:
                        st.write(f"**设备编码**: {plan['equipment_code']}")
                        st.write(f"**维护类型**: {plan['maintenance_type']}")

                    with col2:
                        st.write(f"**计划日期**: {plan['scheduled_date']}")
                        st.write(f"**预计时长**: {plan['estimated_duration']}小时")

                    with col3:
                        status_color = "#FFD700" if plan['status'] == 'scheduled' else "#2E8B57"
                        st.markdown(f"**状态**: <span style='color: {status_color}'>{plan['status_text']}</span>",
                                  unsafe_allow_html=True)

                        if plan['status'] == 'scheduled':
                            if st.button("开始维护", key=f"start_maintenance_{plan['id']}"):
                                start_maintenance(plan['id'])
        else:
            st.info("暂无维护计划")

    with col2:
        st.markdown("##### ➕ 安排维护")

        # 维护安排表单
        with st.form("maintenance_form"):
            # 选择设备
            equipment_options = get_equipment_options()
            selected_equipment = st.selectbox("选择设备", equipment_options)

            # 维护类型
            maintenance_types = ["预防性维护", "修复性维护", "改进性维护", "紧急维护"]
            maintenance_type = st.selectbox("维护类型", maintenance_types)

            # 计划日期
            scheduled_date = st.date_input("计划日期", value=datetime.now().date() + timedelta(days=1))

            # 预计时长
            estimated_duration = st.number_input("预计时长(小时)", min_value=0.5, max_value=48.0, value=2.0, step=0.5)

            # 维护描述
            description = st.text_area("维护描述", placeholder="请描述维护内容和注意事项...")

            # 提交按钮
            if st.form_submit_button("📅 安排维护", use_container_width=True):
                if selected_equipment and maintenance_type:
                    schedule_maintenance(selected_equipment, maintenance_type, scheduled_date, estimated_duration, description)
                else:
                    st.error("请填写完整的维护信息")

    st.markdown("---")

    # 维护历史
    st.markdown("##### 📋 维护历史")

    # 获取设备选项（用于筛选）
    equipment_options_for_filter = get_equipment_options()
    maintenance_types_for_filter = ["预防性维护", "修复性维护", "改进性维护", "紧急维护"]

    # 筛选条件
    col1, col2, col3 = st.columns(3)

    with col1:
        history_equipment = st.selectbox("设备筛选", ["全部"] + [eq['name'] for eq in equipment_options_for_filter])

    with col2:
        history_type = st.selectbox("维护类型筛选", ["全部"] + maintenance_types_for_filter)

    with col3:
        history_period = st.selectbox("时间范围", ["最近7天", "最近30天", "最近90天", "全部"])

    # 获取维护历史
    maintenance_history = get_maintenance_history(history_equipment, history_type, history_period)

    if maintenance_history:
        # 显示维护历史表格
        df_history = pd.DataFrame(maintenance_history)
        st.dataframe(df_history, use_container_width=True)
    else:
        st.info("暂无维护历史记录")

with tab4:
    st.markdown("#### 📈 统计分析")

    # 时间范围选择
    col1, col2 = st.columns([1, 3])

    with col1:
        analysis_days = st.selectbox("统计周期", [7, 14, 30, 90], index=2)

    with col2:
        st.markdown(f"##### 📊 最近 {analysis_days} 天设备统计分析")

    # 获取统计数据
    utilization_stats = get_utilization_statistics(analysis_days)

    if utilization_stats and utilization_stats.get("success"):
        stats_data = utilization_stats.get("data", {})

        # 关键指标
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            avg_utilization = stats_data.get("average_utilization", 0)
            st.metric("平均利用率", f"{avg_utilization:.1f}%",
                     delta=f"{avg_utilization - 75:.1f}%" if avg_utilization >= 75 else None)

        with col2:
            avg_oee = stats_data.get("average_oee", 0)
            st.metric("平均OEE", f"{avg_oee:.1f}%",
                     delta=f"{avg_oee - 80:.1f}%" if avg_oee >= 80 else None)

        with col3:
            total_downtime = stats_data.get("total_downtime", 0)
            st.metric("总停机时间", f"{total_downtime:.1f}h", delta_color="inverse")

        with col4:
            maintenance_count = stats_data.get("maintenance_count", 0)
            st.metric("维护次数", maintenance_count)

        st.markdown("---")

        # 图表分析
        col1, col2 = st.columns(2)

        with col1:
            # 设备利用率趋势
            daily_utilization = stats_data.get("daily_utilization", [])
            if daily_utilization:
                dates = [item['date'] for item in daily_utilization]
                utilizations = [item['utilization'] for item in daily_utilization]

                fig_trend = px.line(
                    x=dates,
                    y=utilizations,
                    title="📈 设备利用率趋势",
                    markers=True
                )
                fig_trend.update_layout(
                    xaxis_title="日期",
                    yaxis_title="利用率 (%)"
                )
                fig_trend = apply_plotly_theme(fig_trend)
                st.plotly_chart(fig_trend, use_container_width=True)

        with col2:
            # 设备OEE对比
            equipment_oee = stats_data.get("equipment_oee", [])
            if equipment_oee:
                equipment_names = [item['equipment_name'] for item in equipment_oee]
                oee_values = [item['oee'] for item in equipment_oee]

                fig_oee = px.bar(
                    x=equipment_names,
                    y=oee_values,
                    title="📊 设备OEE对比",
                    color=oee_values,
                    color_continuous_scale="RdYlGn"
                )
                fig_oee.update_layout(
                    xaxis_title="设备",
                    yaxis_title="OEE (%)"
                )
                fig_oee = apply_plotly_theme(fig_oee)
                st.plotly_chart(fig_oee, use_container_width=True)

        # 详细分析表格
        st.markdown("##### 📋 设备详细分析")

        equipment_details = stats_data.get("equipment_details", [])
        if equipment_details:
            df_details = pd.DataFrame(equipment_details)

            # 格式化显示
            formatted_df = df_details.copy()
            if 'utilization_rate' in formatted_df.columns:
                formatted_df['utilization_rate'] = formatted_df['utilization_rate'].apply(lambda x: f"{x:.1f}%")
            if 'oee' in formatted_df.columns:
                formatted_df['oee'] = formatted_df['oee'].apply(lambda x: f"{x:.1f}%")

            st.dataframe(formatted_df, use_container_width=True)

    else:
        st.error("获取统计数据失败")


# 辅助函数
def get_equipment_list(equipment_type=None, status_filter=None, workshop=None, search=None):
    """获取设备列表"""
    try:
        result = api_client.get_equipment_list(
            equipment_type=equipment_type,
            status_filter=status_filter,
            workshop=workshop,
            search=search
        )

        # 如果API调用失败，返回模拟数据
        if not result.get("success"):
            return get_mock_equipment_data()

        return result
    except Exception as e:
        # 如果出现异常，返回模拟数据
        return get_mock_equipment_data()


def get_mock_equipment_data():
    """获取模拟设备数据"""
    return {
        "success": True,
        "equipment": [
            # L系列生产线设备
            {
                "id": "1",
                "equipment_code": "L01",
                "equipment_name": "生产线01",
                "equipment_type": "生产线",
                "status": "running",
                "workshop": "生产线区域",
                "location": "生产区-L01",
                "utilization_rate": 85.2,
                "oee": 82.1,
                "capacity_per_hour": 50.0,
                "capacity_unit": "件",
                "current_order": "订单A001",
                "estimated_completion": "2024-01-16 18:00"
            },
            {
                "id": "2",
                "equipment_code": "L02",
                "equipment_name": "生产线02",
                "equipment_type": "生产线",
                "status": "stopped",
                "workshop": "生产线区域",
                "location": "生产区-L02",
                "utilization_rate": 0.0,
                "oee": 0.0,
                "capacity_per_hour": 45.0,
                "capacity_unit": "件",
                "stop_reason": "原料短缺",
                "stop_time": "2024-01-16 14:30"
            },
            {
                "id": "3",
                "equipment_code": "L03",
                "equipment_name": "生产线03",
                "equipment_type": "生产线",
                "status": "maintenance",
                "workshop": "生产线区域",
                "location": "生产区-L03",
                "utilization_rate": 0.0,
                "oee": 0.0,
                "capacity_per_hour": 48.0,
                "capacity_unit": "件",
                "maintenance_type": "预防性维护",
                "maintenance_start": "2024-01-16 08:00"
            },
            {
                "id": "4",
                "equipment_code": "L04",
                "equipment_name": "生产线04",
                "equipment_type": "生产线",
                "status": "busy",
                "workshop": "生产线区域",
                "location": "生产区-L04",
                "utilization_rate": 76.3,
                "oee": 74.8,
                "capacity_per_hour": 52.0,
                "capacity_unit": "件",
                "current_order": "订单B002",
                "estimated_completion": "2024-01-16 20:00"
            },
            # Tank系列储罐设备
            {
                "id": "5",
                "equipment_code": "Tank01",
                "equipment_name": "储罐01",
                "equipment_type": "储罐设备",
                "status": "running",
                "workshop": "储罐区域",
                "location": "储罐区-T01",
                "utilization_rate": 68.5,
                "oee": 95.2,
                "capacity_per_hour": 1000.0,
                "capacity_unit": "L",
                "current_level": 750.0,
                "max_capacity": 1000.0,
                "material": "原料A"
            },
            {
                "id": "6",
                "equipment_code": "Tank02",
                "equipment_name": "储罐02",
                "equipment_type": "储罐设备",
                "status": "busy",
                "workshop": "储罐区域",
                "location": "储罐区-T02",
                "utilization_rate": 89.2,
                "oee": 92.8,
                "capacity_per_hour": 1200.0,
                "capacity_unit": "L",
                "current_level": 1100.0,
                "max_capacity": 1200.0,
                "material": "原料B"
            },
            {
                "id": "7",
                "equipment_code": "Tank03",
                "equipment_name": "储罐03",
                "equipment_type": "储罐设备",
                "status": "standby",
                "workshop": "储罐区域",
                "location": "储罐区-T03",
                "utilization_rate": 0.0,
                "oee": 0.0,
                "capacity_per_hour": 800.0,
                "capacity_unit": "L",
                "current_level": 0.0,
                "max_capacity": 800.0,
                "material": "空置"
            },
            {
                "id": "8",
                "equipment_code": "Tank04",
                "equipment_name": "储罐04",
                "equipment_type": "储罐设备",
                "status": "maintenance",
                "workshop": "储罐区域",
                "location": "储罐区-T04",
                "utilization_rate": 0.0,
                "oee": 0.0,
                "capacity_per_hour": 1500.0,
                "capacity_unit": "L",
                "current_level": 0.0,
                "max_capacity": 1500.0,
                "material": "维护中",
                "maintenance_type": "清洗维护"
            },
            {
                "id": "9",
                "equipment_code": "Tank05",
                "equipment_name": "储罐05",
                "equipment_type": "储罐设备",
                "status": "available",
                "workshop": "储罐区域",
                "location": "储罐区-T05",
                "utilization_rate": 65.8,
                "oee": 91.3,
                "capacity_per_hour": 900.0,
                "capacity_unit": "L",
                "current_level": 450.0,
                "max_capacity": 900.0,
                "material": "原料C"
            },
            # 传统设备
            {
                "id": "10",
                "equipment_code": "CNC001",
                "equipment_name": "数控加工中心A",
                "equipment_type": "加工中心",
                "status": "available",
                "workshop": "车间A",
                "location": "A区-01号位",
                "utilization_rate": 85.2,
                "oee": 82.1,
                "capacity_per_hour": 10.0,
                "capacity_unit": "件"
            }
        ],
        "total": 10,
        "page": 1,
        "page_size": 20,
        "total_pages": 1
    }


def get_equipment_status_overview():
    """获取设备状态概览"""
    try:
        result = api_client.get_equipment_status_overview()

        # 如果API调用失败，返回模拟数据
        if not result.get("success"):
            return {
                "success": True,
                "data": {
                    "status_distribution": {
                        "available": 6,
                        "busy": 2,
                        "maintenance": 2,
                        "offline": 0
                    },
                    "type_distribution": {
                        "生产线": 4,
                        "储罐设备": 5,
                        "加工中心": 1
                    },
                    "critical_equipment": [
                        {
                            "id": "1",
                            "code": "L01",
                            "name": "生产线01",
                            "status": "available",
                            "utilization": 85.2,
                            "oee": 82.1
                        },
                        {
                            "id": "2",
                            "code": "L02",
                            "name": "生产线02",
                            "status": "busy",
                            "utilization": 92.8,
                            "oee": 88.5
                        },
                        {
                            "id": "5",
                            "code": "Tank01",
                            "name": "储罐01",
                            "status": "available",
                            "utilization": 68.5,
                            "oee": 95.2
                        },
                        {
                            "id": "6",
                            "code": "Tank02",
                            "name": "储罐02",
                            "status": "busy",
                            "utilization": 89.2,
                            "oee": 92.8
                        },
                        {
                            "id": "3",
                            "code": "L03",
                            "name": "生产线03",
                            "status": "maintenance",
                            "utilization": 0.0,
                            "oee": 0.0
                        }
                    ]
                }
            }

        return result
    except Exception as e:
        # 如果出现异常，返回模拟数据
        return {
            "success": True,
            "data": {
                "status_distribution": {
                    "available": 2,
                    "busy": 1,
                    "maintenance": 1,
                    "offline": 0
                },
                "type_distribution": {
                    "加工中心": 1,
                    "数控车床": 1,
                    "铣床": 1,
                    "钻床": 1
                },
                "critical_equipment": []
            }
        }


def get_status_color(status):
    """获取状态颜色"""
    colors = {
        "running": "#2E8B57",        # 正常运行 - 绿色
        "available": "#32CD32",      # 可用待机 - 浅绿色
        "busy": "#FFD700",           # 忙碌生产 - 黄色
        "stopped": "#FF6347",        # 停线 - 红色
        "maintenance": "#FF4500",    # 维护中 - 橙红色
        "offline": "#808080",        # 离线 - 灰色
        "fault": "#DC143C",          # 故障 - 深红色
        "standby": "#87CEEB"         # 待机 - 天蓝色
    }
    return colors.get(status, "#333333")


def get_status_text(status):
    """获取状态文本"""
    texts = {
        "running": "正常运行",
        "available": "可用待机",
        "busy": "忙碌生产",
        "stopped": "停线",
        "maintenance": "维护中",
        "offline": "离线",
        "fault": "故障",
        "standby": "待机"
    }
    return texts.get(status, "未知")


def get_status_icon(status):
    """获取状态图标"""
    icons = {
        "running": "🟢",
        "available": "🟡",
        "busy": "🔵",
        "stopped": "🔴",
        "maintenance": "🟠",
        "offline": "⚫",
        "fault": "🔴",
        "standby": "⚪"
    }
    return icons.get(status, "❓")


def show_equipment_detail(equipment_id):
    """显示设备详情"""
    # 这里可以实现设备详情弹窗
    st.info(f"显示设备 {equipment_id} 的详情")


def get_maintenance_plans():
    """获取维护计划"""
    # 模拟数据
    return [
        {
            "id": "1",
            "equipment_code": "CNC001",
            "equipment_name": "数控加工中心A",
            "maintenance_type": "预防性维护",
            "scheduled_date": "2024-01-20",
            "estimated_duration": 4,
            "status": "scheduled",
            "status_text": "已安排"
        },
        {
            "id": "2",
            "equipment_code": "CNC002",
            "equipment_name": "数控车床B",
            "maintenance_type": "修复性维护",
            "scheduled_date": "2024-01-18",
            "estimated_duration": 6,
            "status": "in_progress",
            "status_text": "进行中"
        }
    ]


def get_equipment_options():
    """获取设备选项"""
    # 模拟数据
    return [
        {"id": "1", "name": "数控加工中心A", "code": "CNC001"},
        {"id": "2", "name": "数控车床B", "code": "CNC002"},
        {"id": "3", "name": "铣床C", "code": "MILL001"}
    ]


def schedule_maintenance(equipment, maintenance_type, scheduled_date, duration, description):
    """安排维护"""
    try:
        maintenance_data = {
            "equipment_id": equipment["id"],
            "maintenance_type": maintenance_type,
            "scheduled_date": scheduled_date.isoformat(),
            "estimated_duration": duration,
            "description": description
        }

        result = api_client.schedule_equipment_maintenance(equipment["id"], maintenance_data)

        if result.get("success"):
            st.success("维护计划安排成功！")
            time.sleep(1)
            st.rerun()
        else:
            st.error(f"安排维护失败: {result.get('message', '未知错误')}")

    except Exception as e:
        st.error(f"安排维护失败: {str(e)}")


def start_maintenance(plan_id):
    """开始维护"""
    st.success(f"开始维护计划 {plan_id}")


def get_maintenance_history(equipment, maintenance_type, period):
    """获取维护历史"""
    # 模拟数据
    return [
        {
            "设备编码": "CNC001",
            "设备名称": "数控加工中心A",
            "维护类型": "预防性维护",
            "开始时间": "2024-01-10 08:00",
            "结束时间": "2024-01-10 12:00",
            "实际时长": "4小时",
            "状态": "已完成",
            "执行人": "张师傅"
        },
        {
            "设备编码": "CNC002",
            "设备名称": "数控车床B",
            "维护类型": "修复性维护",
            "开始时间": "2024-01-08 14:00",
            "结束时间": "2024-01-08 18:30",
            "实际时长": "4.5小时",
            "状态": "已完成",
            "执行人": "李师傅"
        }
    ]


def get_utilization_statistics(days):
    """获取利用率统计"""
    try:
        result = api_client.get_equipment_utilization_statistics(days)

        # 如果API返回失败，使用模拟数据
        if not result.get("success"):
            return {
                "success": True,
                "data": {
                    "average_utilization": 78.5,
                    "average_oee": 82.3,
                    "total_downtime": 24.5,
                    "maintenance_count": 8,
                    "daily_utilization": [
                        {"date": "2024-01-10", "utilization": 75.2},
                        {"date": "2024-01-11", "utilization": 82.1},
                        {"date": "2024-01-12", "utilization": 79.8},
                        {"date": "2024-01-13", "utilization": 85.3},
                        {"date": "2024-01-14", "utilization": 77.6}
                    ],
                    "equipment_oee": [
                        {"equipment_name": "CNC001", "oee": 85.2},
                        {"equipment_name": "CNC002", "oee": 79.8},
                        {"equipment_name": "MILL001", "oee": 82.1}
                    ],
                    "equipment_details": [
                        {
                            "设备编码": "CNC001",
                            "设备名称": "数控加工中心A",
                            "利用率": 85.2,
                            "OEE": 82.1,
                            "停机时间": 8.5,
                            "维护次数": 2
                        },
                        {
                            "设备编码": "CNC002",
                            "设备名称": "数控车床B",
                            "利用率": 79.8,
                            "OEE": 78.3,
                            "停机时间": 12.2,
                            "维护次数": 3
                        }
                    ]
                }
            }

        return result

    except Exception as e:
        st.error(f"获取利用率统计失败: {str(e)}")
        return None


def change_equipment_status(status_change_data):
    """变更设备状态"""
    try:
        # 这里应该调用API更新设备状态
        # result = api_client.change_equipment_status(status_change_data)

        # 模拟API调用成功
        equipment_id = status_change_data.get("equipment_id")
        old_status = status_change_data.get("old_status")
        new_status = status_change_data.get("new_status")

        old_status_text = get_status_text(old_status)
        new_status_text = get_status_text(new_status)

        st.success(f"设备状态已从 '{old_status_text}' 变更为 '{new_status_text}'")

        # 记录状态变更日志（这里应该保存到数据库）
        st.info("状态变更已记录到系统日志")

        # 关闭模态框并刷新页面
        st.session_state.show_status_modal = False
        st.session_state.change_status_equipment_id = None
        time.sleep(1)
        st.rerun()

    except Exception as e:
        st.error(f"状态变更失败: {str(e)}")


def get_equipment_data_for_llm():
    """获取设备数据用于LLM和算法分析"""
    equipment_data = get_mock_equipment_data()

    if equipment_data.get("success"):
        equipment_list = equipment_data.get("equipment", [])

        # 格式化设备数据为LLM友好的格式
        llm_data = {
            "equipment_summary": {
                "total_count": len(equipment_list),
                "production_lines": [],
                "tanks": [],
                "traditional_equipment": []
            },
            "current_status": {},
            "capacity_info": {},
            "constraints": []
        }

        for eq in equipment_list:
            equipment_info = {
                "code": eq.get("equipment_code"),
                "name": eq.get("equipment_name"),
                "type": eq.get("equipment_type"),
                "status": eq.get("status"),
                "status_text": get_status_text(eq.get("status")),
                "workshop": eq.get("workshop"),
                "location": eq.get("location"),
                "capacity_per_hour": eq.get("capacity_per_hour"),
                "capacity_unit": eq.get("capacity_unit"),
                "utilization_rate": eq.get("utilization_rate"),
                "oee": eq.get("oee")
            }

            # 添加特殊信息
            if eq.get("current_order"):
                equipment_info["current_order"] = eq.get("current_order")
                equipment_info["estimated_completion"] = eq.get("estimated_completion")

            if eq.get("current_level") is not None:
                equipment_info["current_level"] = eq.get("current_level")
                equipment_info["max_capacity"] = eq.get("max_capacity")
                equipment_info["material"] = eq.get("material")

            # 分类设备
            if eq.get("equipment_type") == "生产线":
                llm_data["equipment_summary"]["production_lines"].append(equipment_info)
            elif eq.get("equipment_type") == "储罐设备":
                llm_data["equipment_summary"]["tanks"].append(equipment_info)
            else:
                llm_data["equipment_summary"]["traditional_equipment"].append(equipment_info)

            # 记录当前状态
            llm_data["current_status"][eq.get("equipment_code")] = {
                "status": eq.get("status"),
                "available_for_planning": eq.get("status") in ["running", "available", "standby"],
                "capacity_available": eq.get("capacity_per_hour") if eq.get("status") in ["running", "available", "standby"] else 0
            }

            # 记录产能信息
            llm_data["capacity_info"][eq.get("equipment_code")] = {
                "max_capacity_per_hour": eq.get("capacity_per_hour"),
                "current_utilization": eq.get("utilization_rate"),
                "effective_capacity": eq.get("capacity_per_hour") * (eq.get("utilization_rate", 0) / 100)
            }

            # 添加约束条件
            if eq.get("status") == "stopped":
                llm_data["constraints"].append({
                    "equipment": eq.get("equipment_code"),
                    "type": "unavailable",
                    "reason": eq.get("stop_reason", "停线"),
                    "impact": "无法安排生产任务"
                })
            elif eq.get("status") == "maintenance":
                llm_data["constraints"].append({
                    "equipment": eq.get("equipment_code"),
                    "type": "maintenance",
                    "reason": eq.get("maintenance_type", "维护中"),
                    "impact": "维护期间无法使用"
                })

        return llm_data

    return None


def export_equipment_report():
    """导出设备报表"""
    st.success("设备报表导出功能开发中...")





def show_create_equipment_modal():
    """显示创建设备模态框"""
    with st.modal("➕ 添加设备"):
        st.markdown("### 设备基本信息")

        with st.form("create_equipment_form"):
            # 设备类型选择
            equipment_type = st.selectbox("设备类型*", ["生产线", "储罐设备", "加工中心", "数控车床", "铣床", "钻床", "磨床", "装配线"])

            col1, col2 = st.columns(2)

            with col1:
                # 根据设备类型提供不同的编码建议
                if equipment_type == "生产线":
                    placeholder_code = "例如: L05, L06..."
                    placeholder_name = "例如: 生产线05"
                    default_unit = "件"
                    default_capacity = 50.0
                elif equipment_type == "储罐设备":
                    placeholder_code = "例如: Tank06, Tank07..."
                    placeholder_name = "例如: 储罐06"
                    default_unit = "L"
                    default_capacity = 1000.0
                else:
                    placeholder_code = "例如: CNC002, MILL002..."
                    placeholder_name = "例如: 数控加工中心B"
                    default_unit = "件"
                    default_capacity = 10.0

                equipment_code = st.text_input("设备编码*", placeholder=placeholder_code)
                equipment_name = st.text_input("设备名称*", placeholder=placeholder_name)

            with col2:
                # 根据设备类型提供不同的车间选项
                if equipment_type == "生产线":
                    workshop_options = ["生产线区域", "车间A", "车间B", "车间C"]
                    default_workshop = "生产线区域"
                    location_placeholder = "例如: 生产区-L05"
                elif equipment_type == "储罐设备":
                    workshop_options = ["储罐区域", "车间A", "车间B", "车间C"]
                    default_workshop = "储罐区域"
                    location_placeholder = "例如: 储罐区-T06"
                else:
                    workshop_options = ["车间A", "车间B", "车间C", "装配车间"]
                    default_workshop = "车间A"
                    location_placeholder = "例如: A区-03号位"

                workshop = st.selectbox("所属区域*", workshop_options, index=0)
                location = st.text_input("设备位置", placeholder=location_placeholder)

            # 产能信息
            col1, col2 = st.columns(2)
            with col1:
                capacity_per_hour = st.number_input("每小时产能", min_value=0.0, value=default_capacity)
            with col2:
                capacity_unit = st.text_input("产能单位", value=default_unit, placeholder="例如: 件、L、kg、m")

            # 设备规格
            st.markdown("### 设备规格")

            # 根据设备类型提供不同的规格模板
            if equipment_type == "生产线":
                spec_placeholder = """生产线规格示例:
- 生产能力: 50件/小时
- 工位数量: 8个
- 传送带长度: 50米
- 电源要求: 380V/50Hz
- 占地面积: 100平方米"""
            elif equipment_type == "储罐设备":
                spec_placeholder = """储罐规格示例:
- 容量: 1000L
- 材质: 不锈钢304
- 工作压力: 0.6MPa
- 工作温度: -10°C~80°C
- 进出口径: DN50"""
            else:
                spec_placeholder = "请输入设备的详细规格参数..."

            specifications = st.text_area("设备规格", placeholder=spec_placeholder, height=120)

            # 提交按钮
            col1, col2 = st.columns(2)

            with col1:
                if st.form_submit_button("✅ 创建设备", use_container_width=True):
                    if equipment_code and equipment_name and equipment_type and workshop:
                        create_new_equipment({
                            "equipment_code": equipment_code,
                            "equipment_name": equipment_name,
                            "equipment_type": equipment_type,
                            "workshop": workshop,
                            "location": location,
                            "capacity_per_hour": capacity_per_hour,
                            "capacity_unit": capacity_unit,
                            "specifications": {"description": specifications} if specifications else None
                        })
                    else:
                        st.error("请填写所有必填字段（标*）")

            with col2:
                if st.form_submit_button("❌ 取消", use_container_width=True):
                    st.session_state.show_create_modal = False
                    st.rerun()


def show_edit_equipment_modal():
    """显示编辑设备模态框"""
    equipment_id = st.session_state.get('edit_equipment_id')

    if equipment_id:
        with st.modal("✏️ 编辑设备"):
            st.markdown("### 编辑设备信息")

            # 获取设备详情
            equipment_detail = get_equipment_detail_for_edit(equipment_id)

            if equipment_detail:
                with st.form("edit_equipment_form"):
                    col1, col2 = st.columns(2)

                    with col1:
                        equipment_name = st.text_input("设备名称", value=equipment_detail.get("equipment_name", ""))
                        equipment_type = st.selectbox("设备类型",
                                                    ["加工中心", "数控车床", "铣床", "钻床", "磨床", "装配线"],
                                                    index=0)  # 应该根据当前值设置
                        status = st.selectbox("设备状态", ["available", "busy", "maintenance", "offline"])

                    with col2:
                        workshop = st.selectbox("所属车间", ["车间A", "车间B", "车间C", "装配车间"])
                        location = st.text_input("设备位置", value=equipment_detail.get("location", ""))
                        capacity_per_hour = st.number_input("每小时产能",
                                                          value=equipment_detail.get("capacity_per_hour", 0.0))

                    capacity_unit = st.text_input("产能单位", value=equipment_detail.get("capacity_unit", ""))

                    # 设备规格
                    current_specs = equipment_detail.get("specifications", {})
                    specifications = st.text_area("设备规格",
                                                 value=current_specs.get("description", "") if current_specs else "")

                    # 提交按钮
                    col1, col2 = st.columns(2)

                    with col1:
                        if st.form_submit_button("✅ 保存修改", use_container_width=True):
                            update_equipment_info(equipment_id, {
                                "equipment_name": equipment_name,
                                "equipment_type": equipment_type,
                                "status": status,
                                "workshop": workshop,
                                "location": location,
                                "capacity_per_hour": capacity_per_hour,
                                "capacity_unit": capacity_unit,
                                "specifications": {"description": specifications} if specifications else None
                            })

                    with col2:
                        if st.form_submit_button("❌ 取消", use_container_width=True):
                            st.session_state.show_edit_modal = False
                            st.session_state.edit_equipment_id = None
                            st.rerun()
            else:
                st.error("获取设备信息失败")


def create_new_equipment(equipment_data):
    """创建新设备"""
    try:
        result = api_client.create_equipment(equipment_data)

        if result.get("success"):
            st.success("设备创建成功！")
            st.session_state.show_create_modal = False
            time.sleep(1)
            st.rerun()
        else:
            st.error(f"创建设备失败: {result.get('message', '未知错误')}")

    except Exception as e:
        st.error(f"创建设备失败: {str(e)}")


def get_equipment_detail_for_edit(equipment_id):
    """获取设备详情用于编辑"""
    try:
        result = api_client.get_equipment_detail(equipment_id)

        if result.get("success"):
            return result.get("data")
        else:
            return None

    except Exception as e:
        st.error(f"获取设备详情失败: {str(e)}")
        return None


def update_equipment_info(equipment_id, equipment_data):
    """更新设备信息"""
    try:
        result = api_client.update_equipment(equipment_id, equipment_data)

        if result.get("success"):
            st.success("设备信息更新成功！")
            st.session_state.show_edit_modal = False
            st.session_state.edit_equipment_id = None
            time.sleep(1)
            st.rerun()
        else:
            st.error(f"更新设备失败: {result.get('message', '未知错误')}")

    except Exception as e:
        st.error(f"更新设备失败: {str(e)}")


def show_status_change_modal():
    """显示设备状态切换模态框"""
    equipment_id = st.session_state.get('change_status_equipment_id')

    if equipment_id:
        # 获取设备信息
        equipment_data = get_mock_equipment_data()
        equipment = None
        for eq in equipment_data.get("equipment", []):
            if eq.get("id") == equipment_id:
                equipment = eq
                break

        if equipment:
            with st.modal("🔄 设备状态切换"):
                st.markdown(f"### {equipment.get('equipment_code')} - {equipment.get('equipment_name')}")

                # 显示当前状态
                current_status = equipment.get('status')
                current_status_text = get_status_text(current_status)
                current_status_icon = get_status_icon(current_status)

                st.markdown(f"**当前状态**: {current_status_icon} {current_status_text}")

                with st.form("status_change_form"):
                    # 状态选择
                    status_options = [
                        ("running", "🟢 正常运行"),
                        ("available", "🟡 可用待机"),
                        ("busy", "🔵 忙碌生产"),
                        ("stopped", "🔴 停线"),
                        ("maintenance", "🟠 维护中"),
                        ("offline", "⚫ 离线"),
                        ("fault", "🔴 故障"),
                        ("standby", "⚪ 待机")
                    ]

                    # 找到当前状态的索引
                    current_index = 0
                    for i, (status_code, _) in enumerate(status_options):
                        if status_code == current_status:
                            current_index = i
                            break

                    new_status = st.selectbox(
                        "选择新状态",
                        options=[code for code, _ in status_options],
                        format_func=lambda x: next(text for code, text in status_options if code == x),
                        index=current_index
                    )

                    # 状态变更原因
                    reason = st.text_area("状态变更原因", placeholder="请输入状态变更的原因...")

                    # 特殊状态的额外信息
                    if new_status == "stopped":
                        stop_reason = st.text_input("停线原因", placeholder="例如: 原料短缺、设备故障等")
                    elif new_status == "maintenance":
                        maintenance_type = st.selectbox("维护类型", ["预防性维护", "修复性维护", "改进性维护", "紧急维护"])
                        estimated_duration = st.number_input("预计维护时长(小时)", min_value=0.5, value=2.0, step=0.5)
                    elif new_status == "fault":
                        fault_description = st.text_area("故障描述", placeholder="请详细描述故障情况...")

                    # 提交按钮
                    col1, col2 = st.columns(2)

                    with col1:
                        if st.form_submit_button("✅ 确认变更", use_container_width=True):
                            # 构建状态变更数据
                            status_change_data = {
                                "equipment_id": equipment_id,
                                "old_status": current_status,
                                "new_status": new_status,
                                "reason": reason,
                                "timestamp": datetime.now().isoformat()
                            }

                            # 添加特殊状态的额外信息
                            if new_status == "stopped" and 'stop_reason' in locals():
                                status_change_data["stop_reason"] = stop_reason
                            elif new_status == "maintenance" and 'maintenance_type' in locals():
                                status_change_data["maintenance_type"] = maintenance_type
                                status_change_data["estimated_duration"] = estimated_duration
                            elif new_status == "fault" and 'fault_description' in locals():
                                status_change_data["fault_description"] = fault_description

                            # 执行状态变更
                            change_equipment_status(status_change_data)

                    with col2:
                        if st.form_submit_button("❌ 取消", use_container_width=True):
                            st.session_state.show_status_modal = False
                            st.session_state.change_status_equipment_id = None
                            st.rerun()


def show_quick_add_modal():
    """显示快速添加设备模态框"""
    equipment_type = st.session_state.get('quick_add_type', '生产线')

    with st.modal(f"🚀 快速添加{equipment_type}"):
        st.markdown(f"### 快速添加{equipment_type}")

        with st.form("quick_add_form"):
            if equipment_type == "生产线":
                # 自动生成下一个L系列编号
                existing_l_codes = ["L01", "L02", "L03", "L04"]  # 这里应该从数据库获取
                next_number = len(existing_l_codes) + 1
                suggested_code = f"L{next_number:02d}"

                col1, col2 = st.columns(2)

                with col1:
                    equipment_code = st.text_input("设备编码*", value=suggested_code)
                    equipment_name = st.text_input("设备名称*", value=f"生产线{next_number:02d}")

                with col2:
                    workshop = st.selectbox("所属区域*", ["生产线区域"], index=0)
                    location = st.text_input("设备位置", value=f"生产区-{suggested_code}")

                capacity_per_hour = st.number_input("每小时产能", min_value=0.0, value=50.0)
                capacity_unit = st.text_input("产能单位", value="件")

                specifications = st.text_area("设备规格", value=f"""生产线{next_number:02d}规格:
- 生产能力: 50件/小时
- 工位数量: 8个
- 传送带长度: 50米
- 电源要求: 380V/50Hz
- 占地面积: 100平方米""", height=120)

            elif equipment_type == "储罐设备":
                # 自动生成下一个Tank系列编号
                existing_tank_codes = ["Tank01", "Tank02", "Tank03", "Tank04", "Tank05"]  # 这里应该从数据库获取
                next_number = len(existing_tank_codes) + 1
                suggested_code = f"Tank{next_number:02d}"

                col1, col2 = st.columns(2)

                with col1:
                    equipment_code = st.text_input("设备编码*", value=suggested_code)
                    equipment_name = st.text_input("设备名称*", value=f"储罐{next_number:02d}")

                with col2:
                    workshop = st.selectbox("所属区域*", ["储罐区域"], index=0)
                    location = st.text_input("设备位置", value=f"储罐区-T{next_number:02d}")

                capacity_per_hour = st.number_input("容量", min_value=0.0, value=1000.0)
                capacity_unit = st.text_input("容量单位", value="L")

                specifications = st.text_area("设备规格", value=f"""储罐{next_number:02d}规格:
- 容量: 1000L
- 材质: 不锈钢304
- 工作压力: 0.6MPa
- 工作温度: -10°C~80°C
- 进出口径: DN50""", height=120)

            # 提交按钮
            col1, col2 = st.columns(2)

            with col1:
                if st.form_submit_button("✅ 快速创建", use_container_width=True):
                    if equipment_code and equipment_name:
                        create_new_equipment({
                            "equipment_code": equipment_code,
                            "equipment_name": equipment_name,
                            "equipment_type": equipment_type,
                            "workshop": workshop,
                            "location": location,
                            "capacity_per_hour": capacity_per_hour,
                            "capacity_unit": capacity_unit,
                            "specifications": {"description": specifications} if specifications else None
                        })
                        st.session_state.show_quick_add_modal = False
                    else:
                        st.error("请填写设备编码和名称")

            with col2:
                if st.form_submit_button("❌ 取消", use_container_width=True):
                    st.session_state.show_quick_add_modal = False
                    st.rerun()


# 模态框处理
if st.session_state.get('show_create_modal'):
    show_create_equipment_modal()

if st.session_state.get('show_edit_modal'):
    show_edit_equipment_modal()

if st.session_state.get('show_quick_add_modal'):
    show_quick_add_modal()

if st.session_state.get('show_status_modal'):
    show_status_change_modal()
