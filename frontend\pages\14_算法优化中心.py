"""
算法优化中心
专注于算法准确性和AI预测准确性的监控和优化
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.ui_theme import apply_smart_aps_theme, SmartAPSTheme
from services.unified_algorithm_core import UnifiedAlgorithmCore

# 应用主题
apply_smart_aps_theme()

# 页面配置
st.set_page_config(
    page_title="算法优化中心 - Smart APS",
    page_icon="🎯",
    layout="wide"
)

# 页面标题
SmartAPSTheme.create_header_with_logo(
    title="🎯 算法优化中心",
    subtitle="专注于算法准确性和AI预测准确性的优化"
)

# 初始化统一算法核心
@st.cache_resource
def get_algorithm_core():
    return UnifiedAlgorithmCore()

algorithm_core = get_algorithm_core()

# 侧边栏
with st.sidebar:
    st.markdown("### 🎯 优化功能")

    optimization_type = st.selectbox(
        "选择优化类型",
        ["需求预测优化", "生产规划优化", "算法性能监控", "模型对比分析"]
    )

    st.markdown("### 📊 快速统计")
    SmartAPSTheme.create_metric_card("当前准确率", "87.5%", "↑ 12.3%", "positive")
    SmartAPSTheme.create_metric_card("优化次数", "156", "本月", "normal")
    SmartAPSTheme.create_metric_card("模型状态", "正常", "运行中", "positive")

# 主要内容
if optimization_type == "需求预测优化":
    st.markdown("## 📈 需求预测优化")

    col1, col2 = st.columns([2, 1])

    with col1:
        st.markdown("### 📊 历史数据上传")

        uploaded_file = st.file_uploader(
            "上传历史需求数据",
            type=['csv', 'xlsx'],
            help="请上传包含日期和需求量的历史数据"
        )

        if uploaded_file is not None:
            try:
                # 读取数据
                if uploaded_file.name.endswith('.csv'):
                    data = pd.read_csv(uploaded_file)
                else:
                    data = pd.read_excel(uploaded_file)

                st.success(f"数据上传成功！共 {len(data)} 条记录")

                # 数据预览
                st.markdown("#### 数据预览")
                st.dataframe(data.head(), use_container_width=True)

                # 数据质量检查
                st.markdown("#### 数据质量检查")
                col_check1, col_check2, col_check3 = st.columns(3)

                with col_check1:
                    missing_rate = data.isnull().sum().sum() / (len(data) * len(data.columns)) * 100
                    SmartAPSTheme.create_metric_card(
                        "缺失值比例",
                        f"{missing_rate:.1f}%",
                        "需要处理" if missing_rate > 5 else "良好",
                        "warning" if missing_rate > 5 else "positive"
                    )

                with col_check2:
                    duplicate_rate = data.duplicated().sum() / len(data) * 100
                    SmartAPSTheme.create_metric_card(
                        "重复数据比例",
                        f"{duplicate_rate:.1f}%",
                        "需要清理" if duplicate_rate > 1 else "良好",
                        "warning" if duplicate_rate > 1 else "positive"
                    )

                with col_check3:
                    data_completeness = (1 - missing_rate/100) * 100
                    SmartAPSTheme.create_metric_card(
                        "数据完整性",
                        f"{data_completeness:.1f}%",
                        "优秀" if data_completeness > 95 else "一般",
                        "positive" if data_completeness > 95 else "normal"
                    )

                # 开始优化按钮
                if st.button("🚀 开始需求预测优化", type="primary", use_container_width=True):
                    with st.spinner("正在优化需求预测模型..."):
                        # 使用统一算法核心进行优化
                        progress_bar = st.progress(0)
                        status_text = st.empty()

                        # 数据预处理阶段
                        for i in range(25):
                            progress_bar.progress(i + 1)
                            status_text.text("数据质量检查和预处理中...")
                            import time
                            time.sleep(0.02)

                        # 处理数据
                        processed_data, quality_report = algorithm_core.process_data(data, 'demand_forecasting')

                        # 显示数据质量报告
                        if quality_report.quality_score < 0.7:
                            st.warning(f"数据质量分数: {quality_report.quality_score:.2f}")
                            for issue in quality_report.issues:
                                st.warning(f"⚠️ {issue}")
                            for rec in quality_report.recommendations:
                                st.info(f"💡 {rec}")

                        # 模型训练阶段
                        for i in range(25, 75):
                            progress_bar.progress(i + 1)
                            status_text.text("模型训练和调优中...")
                            time.sleep(0.02)

                        # 执行算法优化
                        optimization_result = algorithm_core.optimize_algorithm('demand_forecasting', processed_data)

                        # 验证阶段
                        for i in range(75, 100):
                            progress_bar.progress(i + 1)
                            status_text.text("模型验证和评估中...")
                            time.sleep(0.02)

                        progress_bar.progress(100)
                        status_text.text("优化完成！")

                        # 显示优化结果
                        if optimization_result.success:
                            st.success("🎉 需求预测优化完成！")

                            # 显示性能指标
                            col_result1, col_result2, col_result3 = st.columns(3)

                            with col_result1:
                                SmartAPSTheme.create_metric_card(
                                    "预测准确率",
                                    f"{optimization_result.accuracy:.1%}",
                                    f"R² = {optimization_result.performance_metrics.get('r2', 0):.3f}",
                                    "positive"
                                )

                            with col_result2:
                                SmartAPSTheme.create_metric_card(
                                    "平均绝对误差",
                                    f"{optimization_result.performance_metrics.get('mae', 0):.3f}",
                                    "越小越好",
                                    "positive"
                                )

                            with col_result3:
                                SmartAPSTheme.create_metric_card(
                                    "训练样本数",
                                    f"{optimization_result.model_info.get('training_samples', 0)}",
                                    f"{optimization_result.model_info.get('feature_count', 0)} 个特征",
                                    "normal"
                                )

                            # 显示建议
                            if optimization_result.recommendations:
                                st.markdown("#### 💡 优化建议")
                                for rec in optimization_result.recommendations:
                                    st.info(f"• {rec}")

                            # 显示特征重要性
                            feature_importance = optimization_result.model_info.get('feature_importance', {})
                            if feature_importance:
                                st.markdown("#### 📊 特征重要性")
                                importance_df = pd.DataFrame(
                                    list(feature_importance.items())[:10],
                                    columns=['特征', '重要性']
                                ).sort_values('重要性', ascending=True)

                                fig = px.bar(
                                    importance_df,
                                    x='重要性',
                                    y='特征',
                                    orientation='h',
                                    title="Top 10 重要特征"
                                )
                                fig.update_layout(height=400)
                                st.plotly_chart(fig, use_container_width=True)

                        else:
                            st.error(f"❌ 优化失败: {optimization_result.error_message}")
                            if optimization_result.recommendations:
                                st.markdown("#### 💡 建议")
                                for rec in optimization_result.recommendations:
                                    st.info(f"• {rec}")

            except Exception as e:
                st.error(f"数据读取失败: {str(e)}")

    with col2:
        st.markdown("### 🎯 优化配置")

        # 优化参数设置
        with st.expander("高级参数设置", expanded=False):
            target_accuracy = st.slider("目标准确率", 0.8, 0.99, 0.9, 0.01)
            max_iterations = st.number_input("最大迭代次数", 50, 500, 100)
            validation_split = st.slider("验证集比例", 0.1, 0.3, 0.2, 0.05)

            st.markdown("**特征选择**")
            use_lag_features = st.checkbox("使用滞后特征", True)
            use_seasonal_features = st.checkbox("使用季节性特征", True)
            use_trend_features = st.checkbox("使用趋势特征", True)

        st.markdown("### 📊 模型性能历史")

        # 生成模拟的性能历史数据
        dates = pd.date_range(start="2024-01-01", periods=30, freq="D")
        accuracy_history = np.random.normal(0.85, 0.05, 30)
        accuracy_history = np.clip(accuracy_history, 0.7, 0.95)

        # 创建性能趋势图
        fig = px.line(
            x=dates,
            y=accuracy_history,
            title="预测准确率趋势",
            labels={'x': '日期', 'y': '准确率'}
        )
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='#2C3E50'),
            height=300
        )
        st.plotly_chart(fig, use_container_width=True)

elif optimization_type == "生产规划优化":
    st.markdown("## 📋 生产规划优化")

    col1, col2 = st.columns([3, 1])

    with col1:
        st.markdown("### 📊 生产订单数据")

        # 模拟生产订单数据
        sample_orders = pd.DataFrame({
            '订单号': [f'ORD{i:04d}' for i in range(1, 11)],
            '产品ID': [f'PROD{i%5+1:03d}' for i in range(10)],
            '数量': np.random.randint(100, 1000, 10),
            '优先级': np.random.choice(['高', '中', '低'], 10),
            '交期': pd.date_range(start="2024-02-01", periods=10, freq="D"),
            '预计工时': np.random.randint(10, 100, 10)
        })

        st.dataframe(sample_orders, use_container_width=True)

        # 约束条件设置
        st.markdown("### ⚙️ 约束条件设置")

        col_constraint1, col_constraint2 = st.columns(2)

        with col_constraint1:
            max_capacity = st.number_input("最大产能 (小时/天)", 100, 1000, 480)
            max_overtime = st.number_input("最大加班时间 (小时/天)", 0, 100, 40)

        with col_constraint2:
            setup_time = st.number_input("换线时间 (小时)", 0.5, 10.0, 2.0, 0.5)
            efficiency_target = st.slider("目标效率", 0.7, 1.0, 0.85, 0.05)

        # 优化目标权重
        st.markdown("### 🎯 优化目标权重")

        col_weight1, col_weight2, col_weight3 = st.columns(3)

        with col_weight1:
            makespan_weight = st.slider("完工时间权重", 0.0, 1.0, 0.4, 0.1)

        with col_weight2:
            tardiness_weight = st.slider("延期惩罚权重", 0.0, 1.0, 0.4, 0.1)

        with col_weight3:
            utilization_weight = st.slider("资源利用率权重", 0.0, 1.0, 0.2, 0.1)

        # 开始优化
        if st.button("🚀 开始生产规划优化", type="primary", use_container_width=True):
            with st.spinner("正在优化生产规划..."):
                progress_bar = st.progress(0)
                status_text = st.empty()

                for i in range(100):
                    progress_bar.progress(i + 1)
                    if i < 30:
                        status_text.text("分析约束条件...")
                    elif i < 60:
                        status_text.text("多目标优化中...")
                    elif i < 90:
                        status_text.text("解决方案验证...")
                    else:
                        status_text.text("生成优化报告...")

                    import time
                    time.sleep(0.03)

                st.success("🎉 生产规划优化完成！")

                # 优化结果
                col_opt1, col_opt2, col_opt3 = st.columns(3)

                with col_opt1:
                    SmartAPSTheme.create_metric_card(
                        "计划效率",
                        "91.2%",
                        "↑ 8.5% 提升",
                        "positive"
                    )

                with col_opt2:
                    SmartAPSTheme.create_metric_card(
                        "资源利用率",
                        "88.7%",
                        "↑ 12.3% 提升",
                        "positive"
                    )

                with col_opt3:
                    SmartAPSTheme.create_metric_card(
                        "按时交付率",
                        "95.6%",
                        "↑ 7.8% 提升",
                        "positive"
                    )

    with col2:
        st.markdown("### 📈 优化历史")

        # 优化历史数据
        optimization_dates = pd.date_range(start="2024-01-01", periods=20, freq="D")
        efficiency_history = np.random.normal(0.88, 0.03, 20)
        efficiency_history = np.clip(efficiency_history, 0.8, 0.95)

        fig = px.line(
            x=optimization_dates,
            y=efficiency_history,
            title="规划效率趋势",
            labels={'x': '日期', 'y': '效率'}
        )
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='#2C3E50'),
            height=250
        )
        st.plotly_chart(fig, use_container_width=True)

        st.markdown("### 🎯 优化建议")
        SmartAPSTheme.create_info_card(
            "当前建议",
            "建议增加换线时间缓冲，可提升计划稳定性约5%。",
            "💡"
        )

elif optimization_type == "算法性能监控":
    st.markdown("## 📊 算法性能监控")

    # 性能监控仪表板
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        SmartAPSTheme.create_metric_card(
            "需求预测准确率",
            "89.3%",
            "↑ 2.1% 本周",
            "positive"
        )

    with col2:
        SmartAPSTheme.create_metric_card(
            "规划算法效率",
            "92.7%",
            "↑ 1.8% 本周",
            "positive"
        )

    with col3:
        SmartAPSTheme.create_metric_card(
            "模型响应时间",
            "1.2秒",
            "↓ 0.3秒 优化",
            "positive"
        )

    with col4:
        SmartAPSTheme.create_metric_card(
            "系统可用性",
            "99.8%",
            "稳定运行",
            "positive"
        )

    # 详细监控图表
    col_chart1, col_chart2 = st.columns(2)

    with col_chart1:
        st.markdown("### 📈 准确率趋势监控")

        # 生成监控数据
        dates = pd.date_range(start="2024-01-01", periods=30, freq="D")
        demand_accuracy = np.random.normal(0.89, 0.02, 30)
        planning_accuracy = np.random.normal(0.92, 0.015, 30)

        fig = go.Figure()
        fig.add_trace(go.Scatter(x=dates, y=demand_accuracy, name='需求预测', line=dict(color='#4A90E2')))
        fig.add_trace(go.Scatter(x=dates, y=planning_accuracy, name='生产规划', line=dict(color='#50C878')))

        fig.update_layout(
            title="算法准确率趋势",
            xaxis_title="日期",
            yaxis_title="准确率",
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='#2C3E50'),
            height=400
        )

        st.plotly_chart(fig, use_container_width=True)

    with col_chart2:
        st.markdown("### ⚡ 性能指标监控")

        # 性能指标雷达图
        categories = ['准确率', '响应时间', '稳定性', '可扩展性', '易用性']
        values = [89, 92, 95, 87, 91]

        fig = go.Figure()
        fig.add_trace(go.Scatterpolar(
            r=values,
            theta=categories,
            fill='toself',
            name='当前性能',
            line=dict(color='#4A90E2')
        ))

        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 100]
                )),
            showlegend=True,
            title="算法性能雷达图",
            font=dict(color='#2C3E50'),
            height=400
        )

        st.plotly_chart(fig, use_container_width=True)

else:  # 模型对比分析
    st.markdown("## 🔍 模型对比分析")

    # 模型对比表格
    model_comparison = pd.DataFrame({
        '模型名称': ['随机森林', '梯度提升', '线性回归', '神经网络', '集成模型'],
        '准确率': [89.3, 91.2, 78.5, 87.6, 93.1],
        '训练时间(秒)': [45, 120, 5, 300, 180],
        '预测时间(毫秒)': [12, 8, 2, 15, 20],
        '内存使用(MB)': [256, 180, 32, 512, 380],
        '稳定性': ['高', '高', '中', '中', '很高']
    })

    st.markdown("### 📊 模型性能对比")
    st.dataframe(model_comparison, use_container_width=True)

    # 性能对比图表
    col_comp1, col_comp2 = st.columns(2)

    with col_comp1:
        fig = px.bar(
            model_comparison,
            x='模型名称',
            y='准确率',
            title="模型准确率对比",
            color='准确率',
            color_continuous_scale='Viridis'
        )
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='#2C3E50')
        )
        st.plotly_chart(fig, use_container_width=True)

    with col_comp2:
        fig = px.scatter(
            model_comparison,
            x='训练时间(秒)',
            y='准确率',
            size='内存使用(MB)',
            color='模型名称',
            title="训练时间 vs 准确率",
            hover_data=['预测时间(毫秒)']
        )
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='#2C3E50')
        )
        st.plotly_chart(fig, use_container_width=True)

# 页面底部
st.markdown("---")
st.markdown("### 💡 优化建议")

col_suggestion1, col_suggestion2, col_suggestion3 = st.columns(3)

with col_suggestion1:
    SmartAPSTheme.create_info_card(
        "数据质量",
        "定期清理和验证输入数据，确保算法输入的高质量。",
        "🔍"
    )

with col_suggestion2:
    SmartAPSTheme.create_info_card(
        "模型更新",
        "建议每月重新训练模型，以适应业务变化。",
        "🔄"
    )

with col_suggestion3:
    SmartAPSTheme.create_info_card(
        "性能监控",
        "持续监控算法性能，及时发现和解决问题。",
        "📊"
    )
