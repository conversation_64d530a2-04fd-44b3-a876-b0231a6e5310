"""
UI主题演示页面
展示Smart APS的新UI主题和组件
"""

import streamlit as st
import pandas as pd
import plotly.express as px
from datetime import datetime, timedelta
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.ui_theme import apply_smart_aps_theme, SmartAPSTheme
from components.responsive_layout import ResponsiveLayout, AdaptiveComponents

# 应用主题
apply_smart_aps_theme()

# 页面配置
st.set_page_config(
    page_title="UI主题演示 - Smart APS",
    page_icon="🎨",
    layout="wide"
)

# 页面标题 - 带Logo
SmartAPSTheme.create_header_with_logo(
    title="🎨 Smart APS UI主题演示",
    subtitle="展示清新简洁的用户界面设计"
)

# 侧边栏
with st.sidebar:
    st.markdown("### 🎨 主题特色")
    
    SmartAPSTheme.create_info_card(
        title="设计理念",
        content="简洁清新、柔和渐变、现代化设计",
        icon="💡"
    )
    
    st.markdown("### 🌈 配色方案")
    st.markdown("""
    - **主色调**: 清新蓝绿渐变
    - **背景色**: 柔和白色渐变
    - **强调色**: 温和的状态色彩
    - **文字色**: 深灰色层次
    """)
    
    st.markdown("### 📱 响应式设计")
    st.markdown("""
    - 自适应布局
    - 移动端优化
    - 多设备兼容
    """)

# 主要内容
tab1, tab2, tab3, tab4 = st.tabs(["📊 指标卡片", "🎯 信息卡片", "📈 图表组件", "🔧 交互组件"])

with tab1:
    st.markdown("#### 📊 指标卡片展示")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        SmartAPSTheme.create_metric_card(
            title="生产效率",
            value="85.5%",
            delta="↑ 5.2% 提升",
            delta_color="positive"
        )
    
    with col2:
        SmartAPSTheme.create_metric_card(
            title="设备利用率",
            value="78.3%",
            delta="↑ 2.1% 提升",
            delta_color="positive"
        )
    
    with col3:
        SmartAPSTheme.create_metric_card(
            title="质量合格率",
            value="96.8%",
            delta="↑ 0.5% 提升",
            delta_color="positive"
        )
    
    with col4:
        SmartAPSTheme.create_metric_card(
            title="按时交付率",
            value="92.3%",
            delta="↓ 1.2% 下降",
            delta_color="negative"
        )
    
    st.markdown("---")
    
    # 更多指标示例
    st.markdown("#### 📈 更多指标示例")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        SmartAPSTheme.create_metric_card(
            title="在线用户",
            value="12",
            delta="当前在线",
            delta_color="normal"
        )
    
    with col2:
        SmartAPSTheme.create_metric_card(
            title="系统负载",
            value="45%",
            delta="正常范围",
            delta_color="normal"
        )
    
    with col3:
        SmartAPSTheme.create_metric_card(
            title="数据处理",
            value="1,234",
            delta="↑ 156 今日",
            delta_color="positive"
        )

with tab2:
    st.markdown("#### 🎯 信息卡片展示")
    
    col1, col2 = st.columns(2)
    
    with col1:
        SmartAPSTheme.create_info_card(
            title="综合仪表板",
            content="查看详细的数据可视化仪表板，实时监控生产状态和关键指标。支持多种图表类型和自定义配置。",
            icon="📊"
        )
        
        SmartAPSTheme.create_info_card(
            title="生产规划",
            content="基于数据生成优化的生产计划，提升生产效率和资源利用率。支持多种算法和约束条件。",
            icon="📋"
        )
    
    with col2:
        SmartAPSTheme.create_info_card(
            title="智能助手",
            content="使用AI助手获取专业建议，解答生产管理相关问题。支持自然语言交互和智能分析。",
            icon="🤖"
        )
        
        SmartAPSTheme.create_info_card(
            title="数据分析",
            content="深入分析生产数据，发现潜在问题和优化机会。提供多维度分析和预测功能。",
            icon="📈"
        )
    
    st.markdown("---")
    
    # 状态徽章示例
    st.markdown("#### 🏷️ 状态徽章")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown("**成功状态**")
        st.markdown(SmartAPSTheme.create_status_badge("运行正常", "success"), unsafe_allow_html=True)
        st.markdown(SmartAPSTheme.create_status_badge("已完成", "success"), unsafe_allow_html=True)
    
    with col2:
        st.markdown("**警告状态**")
        st.markdown(SmartAPSTheme.create_status_badge("需要注意", "warning"), unsafe_allow_html=True)
        st.markdown(SmartAPSTheme.create_status_badge("维护中", "warning"), unsafe_allow_html=True)
    
    with col3:
        st.markdown("**错误状态**")
        st.markdown(SmartAPSTheme.create_status_badge("系统异常", "error"), unsafe_allow_html=True)
        st.markdown(SmartAPSTheme.create_status_badge("连接失败", "error"), unsafe_allow_html=True)
    
    with col4:
        st.markdown("**信息状态**")
        st.markdown(SmartAPSTheme.create_status_badge("处理中", "info"), unsafe_allow_html=True)
        st.markdown(SmartAPSTheme.create_status_badge("待审核", "default"), unsafe_allow_html=True)

with tab3:
    st.markdown("#### 📈 图表组件展示")
    
    # 生成示例数据
    dates = pd.date_range(start="2024-01-01", periods=30, freq="D")
    production_data = pd.DataFrame({
        "日期": dates,
        "生产量": [100 + i * 2 + (i % 7) * 10 for i in range(30)],
        "质量分数": [85 + i * 0.3 + (i % 5) * 2 for i in range(30)],
        "设备利用率": [70 + i * 0.5 + (i % 3) * 5 for i in range(30)]
    })
    
    col1, col2 = st.columns(2)
    
    with col1:
        # 生产趋势图
        fig1 = px.line(
            production_data, 
            x="日期", 
            y="生产量",
            title="📈 生产量趋势",
            color_discrete_sequence=["#4A90E2"]
        )
        fig1.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='#2C3E50'),
            title_font_size=16
        )
        st.plotly_chart(fig1, use_container_width=True)
    
    with col2:
        # 质量分数图
        fig2 = px.bar(
            production_data.tail(10), 
            x="日期", 
            y="质量分数",
            title="📊 质量分数",
            color_discrete_sequence=["#50C878"]
        )
        fig2.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='#2C3E50'),
            title_font_size=16
        )
        st.plotly_chart(fig2, use_container_width=True)
    
    # 进度卡片
    st.markdown("#### 📊 进度卡片")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        AdaptiveComponents.create_progress_card("生产进度", 75.5, "#4A90E2")
    
    with col2:
        AdaptiveComponents.create_progress_card("质量目标", 92.3, "#50C878")
    
    with col3:
        AdaptiveComponents.create_progress_card("交付进度", 88.7, "#FAAD14")

with tab4:
    st.markdown("#### 🔧 交互组件展示")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("##### 📝 表单组件")
        
        # 输入框
        user_input = st.text_input("用户名", placeholder="请输入用户名")
        
        # 选择框
        department = st.selectbox(
            "部门选择",
            ["生产部", "质量部", "设备部", "计划部"]
        )
        
        # 滑块
        priority = st.slider("优先级", 1, 10, 5)
        
        # 日期选择
        selected_date = st.date_input("选择日期", datetime.now().date())
        
        # 多选框
        options = st.multiselect(
            "功能权限",
            ["数据查看", "数据编辑", "系统配置", "用户管理"],
            default=["数据查看"]
        )
    
    with col2:
        st.markdown("##### 🎛️ 控制组件")
        
        # 开关
        enable_notifications = st.checkbox("启用通知", value=True)
        
        # 单选按钮
        theme_choice = st.radio(
            "主题选择",
            ["浅色主题", "深色主题", "自动切换"]
        )
        
        # 文件上传
        uploaded_file = st.file_uploader(
            "选择文件",
            type=['xlsx', 'csv', 'txt']
        )
        
        # 按钮组
        col_btn1, col_btn2 = st.columns(2)
        
        with col_btn1:
            if st.button("保存设置", type="primary", use_container_width=True):
                st.success("设置已保存！")
        
        with col_btn2:
            if st.button("重置", use_container_width=True):
                st.info("设置已重置！")
    
    st.markdown("---")
    
    # 状态指示器
    st.markdown("##### 🚦 状态指示器")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown("**系统状态**")
        st.markdown(AdaptiveComponents.create_status_indicator("success", "系统正常"), unsafe_allow_html=True)
    
    with col2:
        st.markdown("**网络状态**")
        st.markdown(AdaptiveComponents.create_status_indicator("warning", "网络延迟"), unsafe_allow_html=True)
    
    with col3:
        st.markdown("**数据库状态**")
        st.markdown(AdaptiveComponents.create_status_indicator("success", "连接正常"), unsafe_allow_html=True)
    
    with col4:
        st.markdown("**服务状态**")
        st.markdown(AdaptiveComponents.create_status_indicator("info", "运行中"), unsafe_allow_html=True)

# 页面底部信息
st.markdown("---")
st.markdown("#### 💡 主题特点")

col1, col2, col3 = st.columns(3)

with col1:
    st.markdown("""
    **🎨 设计理念**
    - 简洁清新的视觉风格
    - 柔和的渐变配色
    - 现代化的界面设计
    - 一致的交互体验
    """)

with col2:
    st.markdown("""
    **📱 响应式设计**
    - 自适应不同屏幕尺寸
    - 移动端友好界面
    - 灵活的布局系统
    - 优化的触控体验
    """)

with col3:
    st.markdown("""
    **⚡ 性能优化**
    - 轻量级CSS样式
    - 快速加载速度
    - 流畅的动画效果
    - 优化的用户体验
    """)
