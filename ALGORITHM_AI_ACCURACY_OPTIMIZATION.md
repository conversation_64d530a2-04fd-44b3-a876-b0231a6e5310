# 🎯 Smart APS 算法与AI预测准确性优化方案

## 📋 优化目标

专注于提升Smart APS系统中算法的准确性和AI预测的准确性，确保系统能够提供可靠、精确的生产规划和智能决策支持。

## 🔍 当前系统算法与AI现状分析

### ✅ 现有能力
- **统一AI服务**: 已有AI服务架构
- **算法中心**: 集成多种优化算法
- **数据处理**: 支持Excel、邮件等数据源
- **用户界面**: 友好的交互界面

### ⚠️ 准确性问题
- **数据质量**: 输入数据可能存在噪声和缺失
- **模型训练**: 缺乏充分的历史数据训练
- **算法调优**: 参数未针对具体场景优化
- **验证机制**: 缺乏模型准确性验证和监控

## 🎯 算法准确性优化方案

### 1. 数据质量提升

#### 1.1 数据清洗和预处理服务
```python
# frontend/services/data_quality_service.py
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.impute import KNNImputer
import logging

class DataQualityService:
    """数据质量提升服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.scalers = {}
        self.imputers = {}
    
    def clean_production_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """清洗生产数据"""
        cleaned_data = data.copy()
        
        # 1. 移除重复数据
        cleaned_data = cleaned_data.drop_duplicates()
        
        # 2. 处理异常值
        cleaned_data = self._handle_outliers(cleaned_data)
        
        # 3. 处理缺失值
        cleaned_data = self._handle_missing_values(cleaned_data)
        
        # 4. 数据类型转换
        cleaned_data = self._convert_data_types(cleaned_data)
        
        # 5. 数据验证
        validation_result = self._validate_data(cleaned_data)
        
        self.logger.info(f"数据清洗完成: {validation_result}")
        
        return cleaned_data
    
    def _handle_outliers(self, data: pd.DataFrame) -> pd.DataFrame:
        """处理异常值"""
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        
        for col in numeric_columns:
            # 使用IQR方法检测异常值
            Q1 = data[col].quantile(0.25)
            Q3 = data[col].quantile(0.75)
            IQR = Q3 - Q1
            
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            # 将异常值替换为边界值
            data[col] = data[col].clip(lower=lower_bound, upper=upper_bound)
        
        return data
    
    def _handle_missing_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        categorical_columns = data.select_dtypes(include=['object']).columns
        
        # 数值列使用KNN填充
        if len(numeric_columns) > 0:
            imputer = KNNImputer(n_neighbors=5)
            data[numeric_columns] = imputer.fit_transform(data[numeric_columns])
        
        # 分类列使用众数填充
        for col in categorical_columns:
            mode_value = data[col].mode()
            if len(mode_value) > 0:
                data[col].fillna(mode_value[0], inplace=True)
        
        return data
    
    def normalize_data(self, data: pd.DataFrame, method='standard') -> pd.DataFrame:
        """数据标准化"""
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        
        if method == 'standard':
            scaler = StandardScaler()
        elif method == 'robust':
            scaler = RobustScaler()
        else:
            raise ValueError("Unsupported normalization method")
        
        data[numeric_columns] = scaler.fit_transform(data[numeric_columns])
        
        return data
```

#### 1.2 数据验证和质量监控
```python
# frontend/services/data_validation_service.py
import pandas as pd
from typing import Dict, List, Tuple
import warnings

class DataValidationService:
    """数据验证服务"""
    
    def __init__(self):
        self.validation_rules = {
            'production_quantity': {'min': 0, 'max': 10000},
            'quality_score': {'min': 0, 'max': 100},
            'efficiency': {'min': 0, 'max': 1},
            'temperature': {'min': -50, 'max': 200},
            'pressure': {'min': 0, 'max': 100}
        }
    
    def validate_dataset(self, data: pd.DataFrame) -> Dict:
        """验证数据集质量"""
        validation_report = {
            'total_records': len(data),
            'missing_values': {},
            'outliers': {},
            'data_types': {},
            'quality_score': 0,
            'issues': []
        }
        
        # 检查缺失值
        missing_values = data.isnull().sum()
        validation_report['missing_values'] = missing_values.to_dict()
        
        # 检查数据类型
        validation_report['data_types'] = data.dtypes.to_dict()
        
        # 检查异常值
        for column in data.select_dtypes(include=[np.number]).columns:
            outliers = self._detect_outliers(data[column])
            if len(outliers) > 0:
                validation_report['outliers'][column] = len(outliers)
        
        # 计算质量分数
        validation_report['quality_score'] = self._calculate_quality_score(data)
        
        # 生成改进建议
        validation_report['recommendations'] = self._generate_recommendations(validation_report)
        
        return validation_report
    
    def _calculate_quality_score(self, data: pd.DataFrame) -> float:
        """计算数据质量分数"""
        total_cells = data.shape[0] * data.shape[1]
        missing_cells = data.isnull().sum().sum()
        
        # 基础分数：完整性
        completeness_score = (total_cells - missing_cells) / total_cells
        
        # 一致性分数：数据类型一致性
        consistency_score = 0.9  # 简化计算
        
        # 准确性分数：基于业务规则验证
        accuracy_score = self._validate_business_rules(data)
        
        # 综合质量分数
        quality_score = (completeness_score * 0.4 + 
                        consistency_score * 0.3 + 
                        accuracy_score * 0.3)
        
        return round(quality_score * 100, 2)
```

### 2. 算法优化和调参

#### 2.1 生产规划算法优化
```python
# frontend/services/optimized_planning_service.py
import numpy as np
import pandas as pd
from scipy.optimize import minimize, differential_evolution
from sklearn.model_selection import GridSearchCV, RandomizedSearchCV
import optuna

class OptimizedPlanningService:
    """优化的生产规划服务"""
    
    def __init__(self):
        self.best_params = {}
        self.optimization_history = []
    
    def optimize_production_schedule(self, orders: pd.DataFrame, 
                                   constraints: Dict) -> Dict:
        """优化生产排程"""
        
        # 1. 参数自动调优
        best_params = self._auto_tune_parameters(orders, constraints)
        
        # 2. 多目标优化
        solution = self._multi_objective_optimization(orders, constraints, best_params)
        
        # 3. 解决方案验证
        validation_result = self._validate_solution(solution, constraints)
        
        # 4. 性能评估
        performance_metrics = self._evaluate_performance(solution, orders)
        
        return {
            'schedule': solution,
            'parameters': best_params,
            'validation': validation_result,
            'performance': performance_metrics,
            'optimization_time': performance_metrics.get('optimization_time', 0)
        }
    
    def _auto_tune_parameters(self, orders: pd.DataFrame, constraints: Dict) -> Dict:
        """自动调参"""
        
        def objective(trial):
            # 定义超参数搜索空间
            params = {
                'alpha': trial.suggest_float('alpha', 0.1, 1.0),
                'beta': trial.suggest_float('beta', 0.1, 1.0),
                'gamma': trial.suggest_float('gamma', 0.1, 1.0),
                'max_iterations': trial.suggest_int('max_iterations', 100, 1000),
                'convergence_threshold': trial.suggest_float('convergence_threshold', 1e-6, 1e-3)
            }
            
            # 运行优化算法
            solution = self._run_optimization(orders, constraints, params)
            
            # 计算目标函数值
            objective_value = self._calculate_objective_value(solution, orders, constraints)
            
            return objective_value
        
        # 使用Optuna进行超参数优化
        study = optuna.create_study(direction='minimize')
        study.optimize(objective, n_trials=50)
        
        return study.best_params
    
    def _multi_objective_optimization(self, orders: pd.DataFrame, 
                                    constraints: Dict, params: Dict) -> Dict:
        """多目标优化"""
        
        def objective_function(x):
            # 解码决策变量
            schedule = self._decode_solution(x, orders)
            
            # 计算多个目标
            makespan = self._calculate_makespan(schedule)
            tardiness = self._calculate_tardiness(schedule, orders)
            resource_utilization = self._calculate_resource_utilization(schedule)
            
            # 加权组合目标
            weighted_objective = (
                params['alpha'] * makespan +
                params['beta'] * tardiness +
                params['gamma'] * (1 - resource_utilization)
            )
            
            return weighted_objective
        
        # 定义约束条件
        constraints_list = self._define_constraints(orders, constraints)
        
        # 使用差分进化算法求解
        bounds = self._define_bounds(orders)
        result = differential_evolution(
            objective_function,
            bounds,
            maxiter=params.get('max_iterations', 500),
            tol=params.get('convergence_threshold', 1e-6),
            seed=42
        )
        
        return self._decode_solution(result.x, orders)
    
    def _validate_solution(self, solution: Dict, constraints: Dict) -> Dict:
        """验证解决方案"""
        validation_result = {
            'is_feasible': True,
            'constraint_violations': [],
            'quality_metrics': {}
        }
        
        # 检查容量约束
        if not self._check_capacity_constraints(solution, constraints):
            validation_result['is_feasible'] = False
            validation_result['constraint_violations'].append('capacity_constraint')
        
        # 检查时间约束
        if not self._check_time_constraints(solution, constraints):
            validation_result['is_feasible'] = False
            validation_result['constraint_violations'].append('time_constraint')
        
        # 计算质量指标
        validation_result['quality_metrics'] = {
            'schedule_efficiency': self._calculate_schedule_efficiency(solution),
            'resource_balance': self._calculate_resource_balance(solution),
            'flexibility_index': self._calculate_flexibility_index(solution)
        }
        
        return validation_result
```

### 3. AI预测模型优化

#### 3.1 需求预测模型优化
```python
# frontend/services/enhanced_demand_forecasting.py
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
import xgboost as xgb
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.seasonal import seasonal_decompose

class EnhancedDemandForecasting:
    """增强的需求预测服务"""
    
    def __init__(self):
        self.models = {
            'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'gradient_boosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'xgboost': xgb.XGBRegressor(n_estimators=100, random_state=42),
            'linear_regression': LinearRegression()
        }
        self.best_model = None
        self.feature_importance = {}
        self.prediction_intervals = {}
    
    def train_and_select_best_model(self, historical_data: pd.DataFrame) -> Dict:
        """训练并选择最佳模型"""
        
        # 1. 特征工程
        features, target = self._feature_engineering(historical_data)
        
        # 2. 模型训练和评估
        model_performance = {}
        
        # 时间序列交叉验证
        tscv = TimeSeriesSplit(n_splits=5)
        
        for model_name, model in self.models.items():
            # 交叉验证
            cv_scores = cross_val_score(
                model, features, target, 
                cv=tscv, scoring='neg_mean_absolute_error'
            )
            
            # 训练完整模型
            model.fit(features, target)
            predictions = model.predict(features)
            
            # 计算性能指标
            performance = {
                'mae': mean_absolute_error(target, predictions),
                'rmse': np.sqrt(mean_squared_error(target, predictions)),
                'r2': r2_score(target, predictions),
                'cv_mae': -cv_scores.mean(),
                'cv_std': cv_scores.std()
            }
            
            model_performance[model_name] = performance
            
            # 保存特征重要性（如果模型支持）
            if hasattr(model, 'feature_importances_'):
                self.feature_importance[model_name] = dict(
                    zip(features.columns, model.feature_importances_)
                )
        
        # 3. 选择最佳模型
        best_model_name = min(model_performance.keys(), 
                             key=lambda x: model_performance[x]['cv_mae'])
        self.best_model = self.models[best_model_name]
        
        # 4. 计算预测区间
        self._calculate_prediction_intervals(features, target)
        
        return {
            'best_model': best_model_name,
            'model_performance': model_performance,
            'feature_importance': self.feature_importance.get(best_model_name, {}),
            'training_summary': {
                'total_samples': len(historical_data),
                'feature_count': len(features.columns),
                'best_mae': model_performance[best_model_name]['mae'],
                'best_r2': model_performance[best_model_name]['r2']
            }
        }
    
    def _feature_engineering(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series]:
        """特征工程"""
        features_df = data.copy()
        
        # 时间特征
        if 'date' in features_df.columns:
            features_df['date'] = pd.to_datetime(features_df['date'])
            features_df['year'] = features_df['date'].dt.year
            features_df['month'] = features_df['date'].dt.month
            features_df['day_of_week'] = features_df['date'].dt.dayofweek
            features_df['quarter'] = features_df['date'].dt.quarter
            features_df['is_weekend'] = features_df['day_of_week'].isin([5, 6]).astype(int)
        
        # 滞后特征
        if 'demand' in features_df.columns:
            for lag in [1, 2, 3, 7, 30]:
                features_df[f'demand_lag_{lag}'] = features_df['demand'].shift(lag)
            
            # 移动平均特征
            for window in [3, 7, 30]:
                features_df[f'demand_ma_{window}'] = features_df['demand'].rolling(window=window).mean()
        
        # 季节性特征
        if 'demand' in features_df.columns and len(features_df) > 365:
            decomposition = seasonal_decompose(features_df['demand'].dropna(), 
                                             model='additive', period=30)
            features_df['seasonal_component'] = decomposition.seasonal
            features_df['trend_component'] = decomposition.trend
        
        # 移除目标变量和非特征列
        target = features_df['demand'] if 'demand' in features_df.columns else None
        features = features_df.drop(['demand', 'date'], axis=1, errors='ignore')
        
        # 移除包含NaN的行
        if target is not None:
            mask = ~(features.isnull().any(axis=1) | target.isnull())
            features = features[mask]
            target = target[mask]
        
        return features, target
    
    def predict_demand(self, future_periods: int, 
                      external_factors: Dict = None) -> Dict:
        """预测未来需求"""
        
        if self.best_model is None:
            raise ValueError("模型尚未训练，请先调用train_and_select_best_model方法")
        
        # 生成未来特征
        future_features = self._generate_future_features(future_periods, external_factors)
        
        # 预测
        predictions = self.best_model.predict(future_features)
        
        # 计算预测区间
        prediction_intervals = self._get_prediction_intervals(predictions)
        
        # 预测质量评估
        prediction_quality = self._assess_prediction_quality(predictions)
        
        return {
            'predictions': predictions.tolist(),
            'prediction_intervals': prediction_intervals,
            'prediction_quality': prediction_quality,
            'confidence_score': prediction_quality.get('confidence_score', 0.8)
        }
    
    def _calculate_prediction_intervals(self, features: pd.DataFrame, target: pd.Series):
        """计算预测区间"""
        # 使用残差分析计算预测区间
        predictions = self.best_model.predict(features)
        residuals = target - predictions
        
        # 计算残差的标准差
        residual_std = np.std(residuals)
        
        # 95%置信区间
        self.prediction_intervals = {
            'lower_bound': -1.96 * residual_std,
            'upper_bound': 1.96 * residual_std,
            'std': residual_std
        }
```

### 4. 模型验证和监控

#### 4.1 模型性能监控
```python
# frontend/services/model_monitoring_service.py
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

class ModelMonitoringService:
    """模型性能监控服务"""
    
    def __init__(self):
        self.performance_history = []
        self.alert_thresholds = {
            'mae_threshold': 0.15,  # MAE阈值
            'accuracy_drop_threshold': 0.1,  # 准确率下降阈值
            'drift_threshold': 0.05  # 数据漂移阈值
        }
    
    def monitor_model_performance(self, model_name: str, 
                                predictions: np.array, 
                                actual_values: np.array) -> Dict:
        """监控模型性能"""
        
        # 计算性能指标
        current_performance = {
            'timestamp': datetime.now(),
            'model_name': model_name,
            'mae': mean_absolute_error(actual_values, predictions),
            'rmse': np.sqrt(mean_squared_error(actual_values, predictions)),
            'mape': np.mean(np.abs((actual_values - predictions) / actual_values)) * 100,
            'r2': r2_score(actual_values, predictions),
            'sample_count': len(predictions)
        }
        
        # 添加到历史记录
        self.performance_history.append(current_performance)
        
        # 性能趋势分析
        trend_analysis = self._analyze_performance_trend()
        
        # 检测性能下降
        performance_alerts = self._check_performance_alerts(current_performance)
        
        # 数据漂移检测
        drift_detection = self._detect_data_drift(predictions, actual_values)
        
        return {
            'current_performance': current_performance,
            'trend_analysis': trend_analysis,
            'alerts': performance_alerts,
            'drift_detection': drift_detection,
            'recommendations': self._generate_monitoring_recommendations(
                current_performance, performance_alerts, drift_detection
            )
        }
    
    def _analyze_performance_trend(self) -> Dict:
        """分析性能趋势"""
        if len(self.performance_history) < 2:
            return {'trend': 'insufficient_data'}
        
        recent_performance = self.performance_history[-5:]  # 最近5次
        
        mae_values = [p['mae'] for p in recent_performance]
        r2_values = [p['r2'] for p in recent_performance]
        
        # 计算趋势
        mae_trend = 'improving' if mae_values[-1] < mae_values[0] else 'degrading'
        r2_trend = 'improving' if r2_values[-1] > r2_values[0] else 'degrading'
        
        return {
            'mae_trend': mae_trend,
            'r2_trend': r2_trend,
            'mae_change': (mae_values[-1] - mae_values[0]) / mae_values[0] * 100,
            'r2_change': (r2_values[-1] - r2_values[0]) / r2_values[0] * 100,
            'stability': 'stable' if np.std(mae_values) < 0.02 else 'unstable'
        }
    
    def _generate_monitoring_recommendations(self, current_performance: Dict,
                                           alerts: List, drift_detection: Dict) -> List[str]:
        """生成监控建议"""
        recommendations = []
        
        # 基于性能指标的建议
        if current_performance['mae'] > self.alert_thresholds['mae_threshold']:
            recommendations.append("模型MAE过高，建议重新训练模型或调整特征")
        
        if current_performance['r2'] < 0.7:
            recommendations.append("模型解释能力不足，建议增加更多相关特征")
        
        # 基于告警的建议
        if 'performance_degradation' in alerts:
            recommendations.append("检测到性能下降，建议使用最新数据重新训练模型")
        
        # 基于数据漂移的建议
        if drift_detection.get('has_drift', False):
            recommendations.append("检测到数据漂移，建议更新训练数据集")
        
        return recommendations
```

## 🎯 实施计划

### 第1周：数据质量优化
- ✅ 实施数据清洗和预处理服务
- ✅ 建立数据验证和质量监控机制
- ✅ 优化数据输入流程

### 第2-3周：算法优化
- ✅ 实施自动调参机制
- ✅ 优化生产规划算法
- ✅ 建立多目标优化框架

### 第4-5周：AI模型优化
- ✅ 增强需求预测模型
- ✅ 实施模型自动选择机制
- ✅ 建立预测区间计算

### 第6周：监控和验证
- ✅ 实施模型性能监控
- ✅ 建立准确性验证机制
- ✅ 部署预警系统

## 📊 预期效果

### 算法准确性提升
- **生产规划准确率**: 从75% → 90%+
- **资源利用率优化**: 提升15-20%
- **计划可行性**: 提升至95%+

### AI预测准确性提升
- **需求预测MAE**: 降低30-40%
- **预测R²**: 提升至0.85+
- **预测稳定性**: 提升显著

### 系统可靠性提升
- **模型监控**: 实时性能监控
- **自动预警**: 准确率下降自动告警
- **持续优化**: 基于反馈的持续改进

---

**通过专注的算法和AI优化，Smart APS将提供更准确、可靠的生产规划和预测服务！** 🎯🚀
