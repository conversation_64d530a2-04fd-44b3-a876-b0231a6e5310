"""
统一算法管理页面
避免功能重复，提供统一的算法管理和优化界面
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.ui_theme import apply_smart_aps_theme, SmartAPSTheme
from services.unified_algorithm_core import UnifiedAlgorithmCore
from services.unified_ai_service import UnifiedAIService

# 应用主题
apply_smart_aps_theme()

# 页面配置
st.set_page_config(
    page_title="统一算法管理 - Smart APS",
    page_icon="🎯",
    layout="wide"
)

# 页面标题
SmartAPSTheme.create_header_with_logo(
    title="🎯 统一算法管理中心",
    subtitle="集中管理所有算法和AI服务，避免功能重复，提升用户体验"
)

# 初始化服务
@st.cache_resource
def get_services():
    return UnifiedAlgorithmCore(), UnifiedAIService()

algorithm_core, ai_service = get_services()

# 侧边栏 - 统一导航
with st.sidebar:
    st.markdown("### 🎯 算法管理")
    
    # 统一的功能选择
    selected_function = st.selectbox(
        "选择功能",
        [
            "📊 算法性能监控",
            "🚀 算法优化",
            "🤖 AI预测分析", 
            "📈 模型对比",
            "⚙️ 系统配置"
        ]
    )
    
    st.markdown("---")
    
    # 系统状态概览
    st.markdown("### 📊 系统状态")
    
    # 获取性能报告
    performance_report = algorithm_core.get_performance_report()
    summary = performance_report.get('summary', {})
    
    SmartAPSTheme.create_metric_card(
        "算法总数", 
        str(summary.get('total_algorithms', 4)), 
        "已注册",
        "normal"
    )
    
    SmartAPSTheme.create_metric_card(
        "平均准确率", 
        f"{summary.get('average_accuracy', 0.85):.1%}", 
        "系统整体",
        "positive" if summary.get('average_accuracy', 0) > 0.8 else "warning"
    )
    
    SmartAPSTheme.create_metric_card(
        "健康算法", 
        f"{summary.get('healthy_algorithms', 3)}/{summary.get('total_algorithms', 4)}", 
        "运行正常",
        "positive"
    )

# 主要内容区域
if selected_function == "📊 算法性能监控":
    st.markdown("## 📊 算法性能监控")
    
    # 性能概览
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        SmartAPSTheme.create_metric_card(
            "需求预测", 
            "89.3%", 
            "准确率",
            "positive"
        )
    
    with col2:
        SmartAPSTheme.create_metric_card(
            "生产规划", 
            "92.7%", 
            "效率",
            "positive"
        )
    
    with col3:
        SmartAPSTheme.create_metric_card(
            "质量预测", 
            "85.0%", 
            "准确率",
            "positive"
        )
    
    with col4:
        SmartAPSTheme.create_metric_card(
            "资源优化", 
            "88.0%", 
            "利用率",
            "positive"
        )
    
    # 性能趋势图表
    col_chart1, col_chart2 = st.columns(2)
    
    with col_chart1:
        st.markdown("### 📈 算法准确率趋势")
        
        # 生成模拟数据
        dates = pd.date_range(start="2024-01-01", periods=30, freq="D")
        algorithms = ['需求预测', '生产规划', '质量预测', '资源优化']
        
        fig = go.Figure()
        colors = ['#4A90E2', '#50C878', '#FAAD14', '#FF6B35']
        
        for i, algo in enumerate(algorithms):
            accuracy = np.random.normal(0.87 + i*0.02, 0.02, 30)
            accuracy = np.clip(accuracy, 0.7, 0.95)
            
            fig.add_trace(go.Scatter(
                x=dates, 
                y=accuracy,
                name=algo,
                line=dict(color=colors[i], width=2),
                mode='lines+markers'
            ))
        
        fig.update_layout(
            title="算法准确率趋势对比",
            xaxis_title="日期",
            yaxis_title="准确率",
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='#2C3E50'),
            height=400,
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    with col_chart2:
        st.markdown("### ⚡ 性能指标雷达图")
        
        # 性能雷达图
        categories = ['准确率', '响应时间', '稳定性', '可扩展性', '易用性']
        
        fig = go.Figure()
        
        for i, algo in enumerate(algorithms):
            values = np.random.normal(85, 5, 5)
            values = np.clip(values, 70, 95)
            
            fig.add_trace(go.Scatterpolar(
                r=values,
                theta=categories,
                fill='toself',
                name=algo,
                line=dict(color=colors[i])
            ))
        
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 100]
                )),
            showlegend=True,
            title="算法性能雷达图对比",
            font=dict(color='#2C3E50'),
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)

elif selected_function == "🚀 算法优化":
    st.markdown("## 🚀 统一算法优化")
    
    # 算法选择
    col_select1, col_select2 = st.columns([2, 1])
    
    with col_select1:
        algorithm_type = st.selectbox(
            "选择要优化的算法",
            ["需求预测算法", "生产规划算法", "质量预测算法", "资源优化算法"]
        )
    
    with col_select2:
        optimization_mode = st.selectbox(
            "优化模式",
            ["快速优化", "深度优化", "自定义优化"]
        )
    
    # 数据上传区域
    st.markdown("### 📊 数据上传")
    
    uploaded_file = st.file_uploader(
        "上传训练数据",
        type=['csv', 'xlsx'],
        help="请上传相关的历史数据用于算法训练和优化"
    )
    
    if uploaded_file is not None:
        try:
            # 读取数据
            if uploaded_file.name.endswith('.csv'):
                data = pd.read_csv(uploaded_file)
            else:
                data = pd.read_excel(uploaded_file)
            
            st.success(f"✅ 数据上传成功！共 {len(data)} 条记录，{len(data.columns)} 个字段")
            
            # 数据预览
            with st.expander("📋 数据预览", expanded=False):
                st.dataframe(data.head(10), use_container_width=True)
            
            # 数据质量检查
            st.markdown("### 🔍 数据质量检查")
            
            # 使用统一算法核心进行数据处理
            algorithm_map = {
                "需求预测算法": "demand_forecasting",
                "生产规划算法": "production_planning", 
                "质量预测算法": "quality_prediction",
                "资源优化算法": "resource_optimization"
            }
            
            selected_algorithm = algorithm_map[algorithm_type]
            processed_data, quality_report = algorithm_core.process_data(data, selected_algorithm)
            
            # 显示质量报告
            col_quality1, col_quality2, col_quality3 = st.columns(3)
            
            with col_quality1:
                SmartAPSTheme.create_metric_card(
                    "数据质量分数", 
                    f"{quality_report.quality_score:.1f}分", 
                    "满分100分",
                    "positive" if quality_report.quality_score > 80 else "warning"
                )
            
            with col_quality2:
                SmartAPSTheme.create_metric_card(
                    "缺失值比例", 
                    f"{quality_report.missing_rate:.1f}%", 
                    "需要处理" if quality_report.missing_rate > 5 else "良好",
                    "warning" if quality_report.missing_rate > 5 else "positive"
                )
            
            with col_quality3:
                SmartAPSTheme.create_metric_card(
                    "数据完整性", 
                    f"{100-quality_report.missing_rate:.1f}%", 
                    "可用于训练",
                    "positive"
                )
            
            # 显示问题和建议
            if quality_report.issues:
                st.markdown("#### ⚠️ 发现的问题")
                for issue in quality_report.issues:
                    st.warning(f"• {issue}")
            
            if quality_report.recommendations:
                st.markdown("#### 💡 改进建议")
                for rec in quality_report.recommendations:
                    st.info(f"• {rec}")
            
            # 优化配置
            st.markdown("### ⚙️ 优化配置")
            
            col_config1, col_config2 = st.columns(2)
            
            with col_config1:
                target_accuracy = st.slider("目标准确率", 0.8, 0.99, 0.9, 0.01)
                max_iterations = st.number_input("最大迭代次数", 50, 1000, 100)
            
            with col_config2:
                validation_split = st.slider("验证集比例", 0.1, 0.3, 0.2, 0.05)
                early_stopping = st.checkbox("启用早停", True)
            
            # 开始优化
            if st.button("🚀 开始算法优化", type="primary", use_container_width=True):
                with st.spinner(f"正在优化{algorithm_type}..."):
                    # 构建优化配置
                    config = {
                        'target_accuracy': target_accuracy,
                        'max_iterations': max_iterations,
                        'validation_split': validation_split,
                        'early_stopping': early_stopping
                    }
                    
                    # 执行优化
                    result = algorithm_core.optimize_algorithm(selected_algorithm, processed_data, config)
                    
                    if result.success:
                        st.success("🎉 算法优化完成！")
                        
                        # 显示优化结果
                        col_result1, col_result2, col_result3 = st.columns(3)
                        
                        with col_result1:
                            SmartAPSTheme.create_metric_card(
                                "优化后准确率", 
                                f"{result.accuracy:.1%}", 
                                f"提升 {(result.accuracy - 0.8)*100:.1f}%",
                                "positive"
                            )
                        
                        with col_result2:
                            mae = result.performance_metrics.get('mae', 0)
                            SmartAPSTheme.create_metric_card(
                                "平均绝对误差", 
                                f"{mae:.3f}", 
                                "越小越好",
                                "positive"
                            )
                        
                        with col_result3:
                            training_samples = result.model_info.get('training_samples', 0)
                            SmartAPSTheme.create_metric_card(
                                "训练样本数", 
                                str(training_samples), 
                                "有效数据",
                                "normal"
                            )
                        
                        # 显示建议
                        if result.recommendations:
                            st.markdown("#### 💡 优化建议")
                            for rec in result.recommendations:
                                st.info(f"• {rec}")
                    
                    else:
                        st.error(f"❌ 优化失败: {result.error_message}")
                        if result.recommendations:
                            st.markdown("#### 💡 建议")
                            for rec in result.recommendations:
                                st.info(f"• {rec}")
        
        except Exception as e:
            st.error(f"❌ 数据处理失败: {str(e)}")

elif selected_function == "🤖 AI预测分析":
    st.markdown("## 🤖 AI预测分析")
    
    # AI功能选择
    ai_function = st.selectbox(
        "选择AI功能",
        ["需求预测", "设备故障预测", "质量预测", "综合分析"]
    )
    
    if ai_function == "需求预测":
        st.markdown("### 📈 需求预测")
        
        # 预测参数设置
        col_param1, col_param2 = st.columns(2)
        
        with col_param1:
            forecast_horizon = st.number_input("预测天数", 1, 90, 30)
            product_id = st.text_input("产品ID", "PROD001")
        
        with col_param2:
            confidence_level = st.slider("置信水平", 0.8, 0.99, 0.95, 0.01)
            include_seasonality = st.checkbox("包含季节性", True)
        
        if st.button("🔮 开始预测", type="primary"):
            with st.spinner("正在进行需求预测..."):
                # 模拟预测结果
                dates = pd.date_range(start=datetime.now(), periods=forecast_horizon, freq="D")
                base_demand = 1000
                trend = np.linspace(0, 50, forecast_horizon)
                seasonal = 100 * np.sin(2 * np.pi * np.arange(forecast_horizon) / 7)
                noise = np.random.normal(0, 20, forecast_horizon)
                
                predictions = base_demand + trend + seasonal + noise
                predictions = np.maximum(predictions, 0)  # 确保非负
                
                # 置信区间
                confidence_width = predictions * 0.1
                upper_bound = predictions + confidence_width
                lower_bound = predictions - confidence_width
                
                # 创建预测图表
                fig = go.Figure()
                
                fig.add_trace(go.Scatter(
                    x=dates,
                    y=predictions,
                    mode='lines+markers',
                    name='预测需求',
                    line=dict(color='#4A90E2', width=3)
                ))
                
                fig.add_trace(go.Scatter(
                    x=dates,
                    y=upper_bound,
                    mode='lines',
                    name='上界',
                    line=dict(color='#4A90E2', width=1, dash='dash'),
                    showlegend=False
                ))
                
                fig.add_trace(go.Scatter(
                    x=dates,
                    y=lower_bound,
                    mode='lines',
                    name='下界',
                    line=dict(color='#4A90E2', width=1, dash='dash'),
                    fill='tonexty',
                    fillcolor='rgba(74, 144, 226, 0.2)',
                    showlegend=False
                ))
                
                fig.update_layout(
                    title=f"{product_id} 需求预测 ({forecast_horizon}天)",
                    xaxis_title="日期",
                    yaxis_title="需求量",
                    plot_bgcolor='rgba(0,0,0,0)',
                    paper_bgcolor='rgba(0,0,0,0)',
                    font=dict(color='#2C3E50'),
                    height=500
                )
                
                st.plotly_chart(fig, use_container_width=True)
                
                # 预测统计
                col_stat1, col_stat2, col_stat3 = st.columns(3)
                
                with col_stat1:
                    SmartAPSTheme.create_metric_card(
                        "平均预测需求", 
                        f"{predictions.mean():.0f}", 
                        "单位/天",
                        "normal"
                    )
                
                with col_stat2:
                    SmartAPSTheme.create_metric_card(
                        "预测总需求", 
                        f"{predictions.sum():.0f}", 
                        f"{forecast_horizon}天总计",
                        "normal"
                    )
                
                with col_stat3:
                    SmartAPSTheme.create_metric_card(
                        "预测置信度", 
                        f"{confidence_level:.0%}", 
                        "可信程度",
                        "positive"
                    )

elif selected_function == "📈 模型对比":
    st.markdown("## 📈 模型对比分析")
    
    # 模型对比表格
    model_data = {
        '算法名称': ['需求预测', '生产规划', '质量预测', '资源优化'],
        '当前准确率': [89.3, 92.7, 85.0, 88.0],
        '目标准确率': [92.0, 95.0, 90.0, 92.0],
        '训练时间(分钟)': [15, 45, 25, 35],
        '预测时间(秒)': [0.5, 2.1, 1.2, 1.8],
        '模型状态': ['健康', '健康', '需优化', '健康']
    }
    
    model_df = pd.DataFrame(model_data)
    
    st.markdown("### 📊 算法性能对比")
    st.dataframe(model_df, use_container_width=True)
    
    # 性能对比图表
    col_comp1, col_comp2 = st.columns(2)
    
    with col_comp1:
        fig = px.bar(
            model_df, 
            x='算法名称', 
            y=['当前准确率', '目标准确率'],
            title="准确率对比：当前 vs 目标",
            barmode='group'
        )
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='#2C3E50')
        )
        st.plotly_chart(fig, use_container_width=True)
    
    with col_comp2:
        fig = px.scatter(
            model_df, 
            x='训练时间(分钟)', 
            y='当前准确率',
            size='预测时间(秒)',
            color='算法名称',
            title="训练时间 vs 准确率",
            hover_data=['预测时间(秒)']
        )
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='#2C3E50')
        )
        st.plotly_chart(fig, use_container_width=True)

else:  # 系统配置
    st.markdown("## ⚙️ 系统配置")
    
    # 算法配置
    st.markdown("### 🎯 算法配置")
    
    col_config1, col_config2 = st.columns(2)
    
    with col_config1:
        st.markdown("#### 需求预测配置")
        demand_model = st.selectbox("预测模型", ["随机森林", "梯度提升", "神经网络"])
        demand_retrain = st.selectbox("重训练频率", ["每日", "每周", "每月"])
        
        st.markdown("#### 生产规划配置")
        planning_algorithm = st.selectbox("规划算法", ["遗传算法", "模拟退火", "粒子群优化"])
        planning_horizon = st.number_input("规划周期(天)", 1, 30, 7)
    
    with col_config2:
        st.markdown("#### 质量预测配置")
        quality_threshold = st.slider("质量阈值", 0.8, 0.99, 0.95, 0.01)
        quality_features = st.multiselect("质量特征", ["温度", "压力", "湿度", "速度"], ["温度", "压力"])
        
        st.markdown("#### 系统性能配置")
        max_concurrent = st.number_input("最大并发数", 1, 20, 5)
        cache_enabled = st.checkbox("启用缓存", True)
    
    # 保存配置
    if st.button("💾 保存配置", type="primary"):
        st.success("✅ 配置已保存！")

# 页面底部
st.markdown("---")
st.markdown("### 💡 系统优势")

col_advantage1, col_advantage2, col_advantage3 = st.columns(3)

with col_advantage1:
    SmartAPSTheme.create_info_card(
        "统一管理",
        "所有算法和AI服务集中管理，避免功能重复，提升维护效率。",
        "🎯"
    )

with col_advantage2:
    SmartAPSTheme.create_info_card(
        "无缝集成",
        "算法间数据和功能共享，提供更好的集成体验和协同效果。",
        "🔗"
    )

with col_advantage3:
    SmartAPSTheme.create_info_card(
        "用户友好",
        "统一的界面设计和交互方式，降低学习成本，提升用户体验。",
        "👥"
    )
