"""
统一数据中心 - 整合所有数据管理功能
包含数据上传、数据集成、数据源管理、数据处理等功能
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加服务路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'services'))

try:
    from unified_data_service import unified_data_service, DataRequest, DataSourceType, DataProcessingType
    from data_integration_service import data_integration_service
    from equipment_data_service import equipment_data_service
    from pci_service import pci_service
except ImportError as e:
    st.error(f"无法导入数据服务模块: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="数据中心",
    page_icon="📊",
    layout="wide"
)

st.title("📊 统一数据中心")
st.markdown("### 一站式数据管理、处理和分析平台")

# 侧边栏 - 数据控制面板
with st.sidebar:
    st.markdown("### 🎛️ 数据控制面板")
    
    # 获取数据服务状态
    data_status = unified_data_service.get_data_service_status()
    
    # 数据状态显示
    st.markdown("#### 📊 数据状态")
    
    active_processes = data_status["active_processes"]
    total_processed = data_status["total_processed"]
    
    if active_processes > 0:
        st.success(f"🟢 {active_processes} 个数据处理中")
    else:
        st.info("⏸️ 所有数据处理完成")
    
    st.metric("总处理次数", total_processed)
    
    # 数据源开关
    st.markdown("#### ⚙️ 数据源开关")
    
    processing_status = data_status["processing_status"]
    for source_name, status in processing_status.items():
        source_display_name = {
            "file_upload": "📁 文件上传",
            "email_import": "📧 邮件导入",
            "database": "🗄️ 数据库",
            "api_endpoint": "🔗 API接口",
            "equipment_sensor": "⚙️ 设备传感器",
            "pci_system": "🔬 PCI系统",
            "erp_system": "💼 ERP系统",
            "mes_system": "🏭 MES系统"
        }.get(source_name, source_name)
        
        enabled = st.checkbox(source_display_name, value=status, key=f"source_{source_name}")
        if enabled != status:
            st.info(f"数据源状态已更新: {source_display_name}")
    
    st.markdown("---")
    
    # 快速操作
    st.markdown("#### 🚀 快速操作")
    
    if st.button("📊 数据质量检查", type="primary", use_container_width=True):
        st.session_state.run_quality_check = True
        st.rerun()
    
    if st.button("🔄 数据同步", use_container_width=True):
        st.session_state.run_data_sync = True
        st.rerun()
    
    if st.button("🧹 清理缓存", use_container_width=True):
        st.session_state.clear_cache = True
        st.rerun()

# 主要内容区域
tab1, tab2, tab3, tab4, tab5 = st.tabs([
    "📁 数据上传", 
    "🔗 数据集成", 
    "📊 数据监控", 
    "🔍 数据分析", 
    "⚙️ 数据配置"
])

with tab1:
    st.markdown("#### 📁 数据上传中心")
    
    # 文件上传区域
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("##### 📤 文件上传")
        
        # 支持的文件类型
        st.info("📋 支持的文件格式: Excel (.xlsx), CSV (.csv), JSON (.json), XML (.xml), TXT (.txt)")
        
        uploaded_files = st.file_uploader(
            "选择文件",
            type=['xlsx', 'csv', 'json', 'xml', 'txt'],
            accept_multiple_files=True,
            help="可以同时上传多个文件"
        )
        
        if uploaded_files:
            st.markdown("##### 📋 上传文件列表")
            
            for i, file in enumerate(uploaded_files):
                with st.expander(f"📄 {file.name} ({file.size} bytes)"):
                    col_a, col_b, col_c = st.columns(3)
                    
                    with col_a:
                        processing_type = st.selectbox(
                            "处理类型",
                            ["extraction", "validation", "integration"],
                            format_func=lambda x: {
                                "extraction": "数据提取",
                                "validation": "数据验证", 
                                "integration": "数据集成"
                            }[x],
                            key=f"process_type_{i}"
                        )
                    
                    with col_b:
                        auto_process = st.checkbox("自动处理", value=True, key=f"auto_{i}")
                    
                    with col_c:
                        if st.button("🚀 处理文件", key=f"process_{i}"):
                            # 处理文件
                            file_data = {
                                "name": file.name,
                                "size": file.size,
                                "type": file.type,
                                "content": file.read()
                            }
                            
                            st.session_state[f"process_file_{i}"] = {
                                "file_data": file_data,
                                "processing_type": processing_type,
                                "auto_process": auto_process
                            }
                            st.rerun()
    
    with col2:
        st.markdown("##### 📊 上传统计")
        
        # 获取处理统计
        data_stats = unified_data_service.get_data_statistics()
        
        if "source_statistics" in data_stats:
            file_stats = data_stats["source_statistics"].get("file_upload", {})
            
            st.metric("总上传次数", file_stats.get("total_processed", 0))
            st.metric("成功率", f"{file_stats.get('success_rate', 0)*100:.1f}%")
            st.metric("平均处理时间", f"{file_stats.get('avg_time', 0):.2f}s")
        
        st.markdown("---")
        
        # 邮件导入
        st.markdown("##### 📧 邮件导入")
        
        email_subject = st.text_input("邮件主题", placeholder="输入邮件主题...")
        email_content = st.text_area("邮件内容", placeholder="粘贴邮件内容...", height=100)
        
        if st.button("📧 导入邮件", use_container_width=True):
            if email_content:
                st.session_state.import_email = {
                    "subject": email_subject,
                    "content": email_content
                }
                st.rerun()
            else:
                st.warning("请输入邮件内容")

with tab2:
    st.markdown("#### 🔗 数据集成中心")
    
    # 数据源连接状态
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("##### 🏭 生产系统")
        
        # 设备数据状态
        try:
            equipment_status = equipment_data_service.get_equipment_status()
            st.success(f"✅ 设备数据: {len(equipment_status.get('equipment', []))}台设备")
        except:
            st.error("❌ 设备数据连接失败")
        
        # PCI系统状态
        try:
            pci_status = pci_service.get_pci_status()
            st.success(f"✅ PCI数据: {pci_status.get('total_fs_items', 0)}个物料")
        except:
            st.error("❌ PCI系统连接失败")
    
    with col2:
        st.markdown("##### 🗄️ 外部系统")
        
        # ERP系统
        erp_connected = st.checkbox("ERP系统", value=False)
        if erp_connected:
            st.success("✅ ERP系统已连接")
        else:
            st.info("⏸️ ERP系统未连接")
        
        # MES系统
        mes_connected = st.checkbox("MES系统", value=False)
        if mes_connected:
            st.success("✅ MES系统已连接")
        else:
            st.info("⏸️ MES系统未连接")
    
    with col3:
        st.markdown("##### 📊 数据质量")
        
        # 模拟数据质量指标
        data_quality = np.random.uniform(85, 98)
        st.metric("数据完整度", f"{data_quality:.1f}%")
        
        data_freshness = np.random.uniform(90, 99)
        st.metric("数据新鲜度", f"{data_freshness:.1f}%")
        
        data_accuracy = np.random.uniform(88, 96)
        st.metric("数据准确度", f"{data_accuracy:.1f}%")
    
    st.markdown("---")
    
    # 数据集成配置
    st.markdown("##### ⚙️ 数据集成配置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        sync_interval = st.selectbox(
            "同步频率",
            ["实时", "每5分钟", "每15分钟", "每小时", "每天"],
            index=2
        )
        
        auto_validation = st.checkbox("自动数据验证", value=True)
        
        conflict_resolution = st.selectbox(
            "冲突解决策略",
            ["最新优先", "手动解决", "忽略冲突"],
            index=0
        )
    
    with col2:
        backup_enabled = st.checkbox("启用数据备份", value=True)
        
        compression_enabled = st.checkbox("启用数据压缩", value=False)
        
        encryption_enabled = st.checkbox("启用数据加密", value=True)
    
    if st.button("💾 保存集成配置", type="primary"):
        st.success("✅ 数据集成配置已保存")

with tab3:
    st.markdown("#### 📊 数据监控中心")
    
    # 实时数据流监控
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("##### 📈 数据流量监控")
        
        # 生成模拟数据流量图表
        hours = list(range(24))
        data_volume = [np.random.randint(50, 200) for _ in hours]
        
        fig_volume = px.line(
            x=hours,
            y=data_volume,
            title="24小时数据流量趋势",
            labels={'x': '小时', 'y': '数据量 (MB)'}
        )
        fig_volume.update_layout(height=300)
        st.plotly_chart(fig_volume, use_container_width=True)
        
        # 数据源贡献度
        st.markdown("##### 📊 数据源贡献度")
        
        sources = ['文件上传', '设备传感器', 'PCI系统', '邮件导入', 'API接口']
        contributions = [30, 25, 20, 15, 10]
        
        fig_pie = px.pie(
            values=contributions,
            names=sources,
            title="数据源贡献度分布"
        )
        fig_pie.update_layout(height=300)
        st.plotly_chart(fig_pie, use_container_width=True)
    
    with col2:
        st.markdown("##### 🚨 数据告警")
        
        # 模拟数据告警
        alerts = [
            {"level": "warning", "message": "设备L01数据延迟", "time": "2分钟前"},
            {"level": "info", "message": "PCI数据同步完成", "time": "5分钟前"},
            {"level": "error", "message": "ERP连接超时", "time": "10分钟前"}
        ]
        
        for alert in alerts:
            if alert["level"] == "error":
                st.error(f"🔴 {alert['message']} ({alert['time']})")
            elif alert["level"] == "warning":
                st.warning(f"🟡 {alert['message']} ({alert['time']})")
            else:
                st.info(f"🔵 {alert['message']} ({alert['time']})")
        
        st.markdown("---")
        
        # 系统性能指标
        st.markdown("##### ⚡ 系统性能")
        
        cpu_usage = np.random.uniform(20, 80)
        st.metric("CPU使用率", f"{cpu_usage:.1f}%")
        
        memory_usage = np.random.uniform(40, 70)
        st.metric("内存使用率", f"{memory_usage:.1f}%")
        
        disk_usage = np.random.uniform(30, 60)
        st.metric("磁盘使用率", f"{disk_usage:.1f}%")

with tab4:
    st.markdown("#### 🔍 数据分析中心")
    
    # 数据质量分析
    st.markdown("##### 📊 数据质量分析")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # 数据完整性分析
        st.markdown("**数据完整性分析**")
        
        completeness_data = {
            "数据表": ["订单数据", "设备数据", "物料数据", "生产数据", "质量数据"],
            "完整度": [95.2, 88.7, 92.1, 89.5, 96.8]
        }
        
        df_completeness = pd.DataFrame(completeness_data)
        
        fig_completeness = px.bar(
            df_completeness,
            x="数据表",
            y="完整度",
            title="数据完整度分析",
            color="完整度",
            color_continuous_scale="Viridis"
        )
        fig_completeness.update_layout(height=300)
        st.plotly_chart(fig_completeness, use_container_width=True)
    
    with col2:
        # 数据趋势分析
        st.markdown("**数据增长趋势**")
        
        dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='M')
        growth_data = np.cumsum(np.random.randint(1000, 5000, len(dates)))
        
        fig_growth = px.line(
            x=dates,
            y=growth_data,
            title="数据量增长趋势",
            labels={'x': '日期', 'y': '累计数据量 (条)'}
        )
        fig_growth.update_layout(height=300)
        st.plotly_chart(fig_growth, use_container_width=True)
    
    # 数据关联分析
    st.markdown("##### 🔗 数据关联分析")
    
    # 生成模拟关联矩阵
    data_types = ['订单', '设备', '物料', '生产', '质量']
    correlation_matrix = np.random.rand(5, 5)
    np.fill_diagonal(correlation_matrix, 1)
    
    fig_corr = px.imshow(
        correlation_matrix,
        x=data_types,
        y=data_types,
        title="数据关联度热力图",
        color_continuous_scale="RdYlBu"
    )
    fig_corr.update_layout(height=400)
    st.plotly_chart(fig_corr, use_container_width=True)

with tab5:
    st.markdown("#### ⚙️ 数据配置中心")
    
    # 数据服务配置
    config = data_status["config"]
    
    st.markdown("##### 🔧 服务配置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        max_file_size = st.slider(
            "最大文件大小 (MB)",
            1, 500,
            config["max_file_size"] // (1024*1024)
        )
        
        auto_processing = st.checkbox(
            "启用自动处理",
            config["auto_processing"]
        )
        
        data_validation = st.checkbox(
            "启用数据验证",
            config["data_validation"]
        )
    
    with col2:
        backup_enabled = st.checkbox(
            "启用数据备份",
            config["backup_enabled"]
        )
        
        retention_days = st.slider(
            "数据保留天数",
            7, 365,
            config["retention_days"]
        )
        
        supported_formats = st.multiselect(
            "支持的文件格式",
            [".xlsx", ".csv", ".json", ".xml", ".txt", ".pdf"],
            config["supported_formats"]
        )
    
    if st.button("💾 保存配置"):
        st.success("配置已保存")
    
    st.markdown("---")
    
    # 数据源插件管理
    st.markdown("##### 🔌 数据源插件")
    
    plugins = data_status.get("data_source_plugins", 4)
    st.metric("已安装插件", f"{plugins}个")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📦 安装插件", use_container_width=True):
            st.success("插件安装成功")
    
    with col2:
        if st.button("🔄 更新插件", use_container_width=True):
            st.success("插件更新完成")
    
    with col3:
        if st.button("🗑️ 卸载插件", use_container_width=True):
            st.success("插件卸载完成")

# 处理特殊操作
if st.session_state.get('run_quality_check'):
    st.session_state.run_quality_check = False
    
    with st.spinner("🔍 正在执行数据质量检查..."):
        st.success("✅ 数据质量检查完成！整体质量评分: 92.5分")

if st.session_state.get('run_data_sync'):
    st.session_state.run_data_sync = False
    
    with st.spinner("🔄 正在同步数据..."):
        st.success("✅ 数据同步完成！同步了1,247条记录")

if st.session_state.get('clear_cache'):
    st.session_state.clear_cache = False
    st.success("🧹 缓存已清理")

# 处理文件上传
for key in st.session_state.keys():
    if key.startswith('process_file_'):
        file_info = st.session_state[key]
        del st.session_state[key]
        
        with st.spinner("🔄 正在处理文件..."):
            st.success(f"✅ 文件 {file_info['file_data']['name']} 处理完成！")

# 处理邮件导入
if st.session_state.get('import_email'):
    email_info = st.session_state.import_email
    del st.session_state.import_email
    
    with st.spinner("📧 正在导入邮件..."):
        st.success(f"✅ 邮件 '{email_info['subject']}' 导入完成！")

# 页面底部信息
st.markdown("---")
st.markdown("##### ℹ️ 数据中心说明")

with st.expander("📖 统一数据中心详解"):
    st.markdown("""
    ### 📊 统一数据中心特性
    
    #### 🎯 核心功能
    - **多源数据接入**: 支持文件、邮件、数据库、API等多种数据源
    - **智能数据处理**: 自动提取、验证、转换和集成数据
    - **实时数据监控**: 监控数据流量、质量和系统性能
    - **数据质量管理**: 全面的数据质量分析和改进建议
    - **灵活配置管理**: 支持个性化的数据处理配置
    
    #### 🚀 技术优势
    1. **统一架构**: 所有数据功能在一个平台中管理
    2. **智能处理**: 自动识别数据格式和处理需求
    3. **实时监控**: 实时监控数据流和系统状态
    4. **质量保证**: 多层次的数据验证和质量检查
    5. **扩展性强**: 支持插件化的数据源扩展
    
    #### 📊 应用场景
    - 多源数据整合和标准化
    - 实时数据流处理和监控
    - 数据质量管理和改进
    - 数据备份和恢复管理
    - 数据安全和合规管理
    """)
