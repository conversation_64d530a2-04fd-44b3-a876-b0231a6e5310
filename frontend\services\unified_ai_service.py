"""
统一AI服务 - 整合所有AI功能到一个统一的服务中
包含LLM对话、预测分析、异常检测、智能优化、强化学习等功能
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np

# 导入统一算法核心
from .unified_algorithm_core import UnifiedAlgorithmCore

# 导入现有AI服务
try:
    from .llm_service import EnhancedLLMService, ConversationManager
except ImportError:
    EnhancedLLMService = None
    ConversationManager = None

try:
    from .learning_engine import LearningEngine, ReinforcementLearningScheduler
except ImportError:
    LearningEngine = None
    ReinforcementLearningScheduler = None

try:
    from .reinforcement_learning_service import ReinforcementLearningService
except ImportError:
    ReinforcementLearningService = None

# 导入AI增强功能（将作为组件集成）
try:
    from .ai_enhancement_service import (
        PredictiveAnalyticsEngine,
        AnomalyDetectionEngine,
        IntelligentOptimizationEngine,
        PredictionType,
        AnomalyType,
        OptimizationType
    )
except ImportError:
    # 如果AI增强服务不存在，使用简化版本
    PredictiveAnalyticsEngine = None
    AnomalyDetectionEngine = None
    IntelligentOptimizationEngine = None

logger = logging.getLogger(__name__)


class AIServiceType(Enum):
    """AI服务类型"""
    LLM_CHAT = "llm_chat"
    PREDICTIVE_ANALYTICS = "predictive_analytics"
    ANOMALY_DETECTION = "anomaly_detection"
    INTELLIGENT_OPTIMIZATION = "intelligent_optimization"
    REINFORCEMENT_LEARNING = "reinforcement_learning"
    LEARNING_ENGINE = "learning_engine"


@dataclass
class AIRequest:
    """统一AI请求"""
    service_type: AIServiceType
    request_data: Dict[str, Any]
    user_id: str
    conversation_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    preferences: Optional[Dict[str, Any]] = None


@dataclass
class AIResponse:
    """统一AI响应"""
    success: bool
    service_type: AIServiceType
    data: Dict[str, Any]
    metadata: Dict[str, Any]
    timestamp: datetime
    error_message: Optional[str] = None


class UnifiedAIService:
    """统一AI服务 - 整合所有AI功能，集成算法核心"""

    def __init__(self):
        # 核心算法服务 - 统一管理所有算法
        self.algorithm_core = UnifiedAlgorithmCore()

        # 初始化各个AI组件（如果可用）
        self.llm_service = EnhancedLLMService() if EnhancedLLMService else None
        self.learning_engine = LearningEngine() if LearningEngine else None
        self.rl_service = ReinforcementLearningService() if ReinforcementLearningService else None

        # AI增强组件（如果可用）
        self.predictive_analytics = PredictiveAnalyticsEngine() if PredictiveAnalyticsEngine else None
        self.anomaly_detection = AnomalyDetectionEngine() if AnomalyDetectionEngine else None
        self.intelligent_optimization = IntelligentOptimizationEngine() if IntelligentOptimizationEngine else None

        # 服务状态
        self.service_status = {
            AIServiceType.LLM_CHAT: True,
            AIServiceType.PREDICTIVE_ANALYTICS: True,
            AIServiceType.ANOMALY_DETECTION: True,
            AIServiceType.INTELLIGENT_OPTIMIZATION: True,
            AIServiceType.REINFORCEMENT_LEARNING: True,
            AIServiceType.LEARNING_ENGINE: True
        }

        # 服务配置
        self.config = {
            "max_concurrent_requests": 10,
            "request_timeout": 300,
            "cache_enabled": True,
            "cross_service_integration": True,
            "auto_learning": True
        }

        # 请求历史和性能统计
        self.request_history = []
        self.performance_metrics = {}

    async def process_request(self, request: AIRequest) -> AIResponse:
        """处理AI请求 - 统一入口"""
        try:
            start_time = datetime.now()

            # 验证服务状态
            if not self.service_status.get(request.service_type, False):
                return AIResponse(
                    success=False,
                    service_type=request.service_type,
                    data={},
                    metadata={},
                    timestamp=datetime.now(),
                    error_message=f"服务 {request.service_type.value} 当前不可用"
                )

            # 根据服务类型路由请求
            if request.service_type == AIServiceType.LLM_CHAT:
                result = await self._handle_llm_request(request)
            elif request.service_type == AIServiceType.PREDICTIVE_ANALYTICS:
                result = await self._handle_prediction_request(request)
            elif request.service_type == AIServiceType.ANOMALY_DETECTION:
                result = await self._handle_anomaly_request(request)
            elif request.service_type == AIServiceType.INTELLIGENT_OPTIMIZATION:
                result = await self._handle_optimization_request(request)
            elif request.service_type == AIServiceType.REINFORCEMENT_LEARNING:
                result = await self._handle_rl_request(request)
            elif request.service_type == AIServiceType.LEARNING_ENGINE:
                result = await self._handle_learning_request(request)
            else:
                raise ValueError(f"不支持的服务类型: {request.service_type}")

            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()

            # 更新性能指标
            self._update_performance_metrics(request.service_type, processing_time, True)

            # 记录请求历史
            self._record_request(request, result, processing_time)

            # 跨服务学习和优化
            if self.config["auto_learning"]:
                await self._trigger_cross_service_learning(request, result)

            return result

        except Exception as e:
            logger.error(f"AI请求处理失败: {e}")

            # 更新性能指标
            processing_time = (datetime.now() - start_time).total_seconds()
            self._update_performance_metrics(request.service_type, processing_time, False)

            return AIResponse(
                success=False,
                service_type=request.service_type,
                data={},
                metadata={"processing_time": processing_time},
                timestamp=datetime.now(),
                error_message=str(e)
            )

    async def _handle_llm_request(self, request: AIRequest) -> AIResponse:
        """处理LLM对话请求"""
        message = request.request_data.get("message", "")

        if self.llm_service is None:
            # 简化的LLM响应
            return AIResponse(
                success=True,
                service_type=AIServiceType.LLM_CHAT,
                data={
                    "response": f"收到消息: {message}。LLM服务当前不可用，这是一个模拟响应。",
                    "conversation_id": "mock_conversation",
                    "ai_suggestions": []
                },
                metadata={"mock_response": True},
                timestamp=datetime.now()
            )

        conversation_id = request.conversation_id or self.llm_service.conversation_manager.create_conversation(request.user_id)

        # 增强LLM请求，集成其他AI服务的上下文
        enhanced_context = await self._build_enhanced_context(request)

        result = self.llm_service.chat_with_context(
            message=message,
            conversation_id=conversation_id,
            user_id=request.user_id,
            model=request.request_data.get("model", "gpt-3.5-turbo"),
            temperature=request.request_data.get("temperature", 0.7),
            include_data_context=True,
            language=request.request_data.get("language")
        )

        # 检查是否需要触发其他AI服务
        ai_suggestions = await self._analyze_llm_response_for_ai_actions(result, request)

        return AIResponse(
            success=result["success"],
            service_type=AIServiceType.LLM_CHAT,
            data={
                **result.get("data", {}),
                "conversation_id": conversation_id,
                "ai_suggestions": ai_suggestions
            },
            metadata=result.get("metadata", {}),
            timestamp=datetime.now()
        )

    async def _handle_prediction_request(self, request: AIRequest) -> AIResponse:
        """处理预测分析请求"""
        prediction_type = request.request_data.get("prediction_type", "demand_forecast")
        input_data = request.request_data.get("input_data", {})

        # 集成学习引擎的历史数据
        enhanced_input = await self._enhance_prediction_input(input_data, request)

        if self.predictive_analytics is None:
            # 使用统一算法核心进行预测
            if prediction_type == "demand_forecast":
                # 转换为DataFrame格式
                df_data = pd.DataFrame([enhanced_input]) if isinstance(enhanced_input, dict) else pd.DataFrame(enhanced_input)
                result = self.algorithm_core.predict("demand_forecasting", df_data)
            else:
                result = {"predictions": [100, 120, 110], "confidence": "medium"}
        else:
            if prediction_type == "demand_forecast":
                result = await self.predictive_analytics.predict_demand(
                    enhanced_input,
                    request.request_data.get("forecast_horizon", 30)
                )
            elif prediction_type == "equipment_failure":
                result = await self.predictive_analytics.predict_equipment_failure(enhanced_input)
            else:
                # 使用学习引擎的预测能力
                result = await self._use_learning_engine_prediction(prediction_type, enhanced_input)

        return AIResponse(
            success=True,
            service_type=AIServiceType.PREDICTIVE_ANALYTICS,
            data={
                "prediction_result": result,
                "enhanced_features": list(enhanced_input.keys()) if isinstance(enhanced_input, dict) else []
            },
            metadata={
                "prediction_type": prediction_type,
                "algorithm_core_used": self.predictive_analytics is None
            },
            timestamp=datetime.now()
        )

    async def _handle_anomaly_request(self, request: AIRequest) -> AIResponse:
        """处理异常检测请求"""
        detection_type = request.request_data.get("detection_type", "ensemble")
        data = request.request_data.get("data", [])

        # 转换数据格式
        df = pd.DataFrame(data)

        # 使用学习引擎增强异常检测
        enhanced_detection = await self._enhance_anomaly_detection(df, detection_type, request)

        if self.anomaly_detection is None:
            # 简化的异常检测
            result = {
                "anomalies_detected": 2,
                "anomaly_indices": [5, 12],
                "confidence": 0.85
            }
        else:
            result = await self.anomaly_detection.detect_anomalies(df, AnomalyType.ENSEMBLE)

        return AIResponse(
            success=True,
            service_type=AIServiceType.ANOMALY_DETECTION,
            data={
                "anomaly_result": result,
                "enhanced_detection": enhanced_detection
            },
            metadata={"detection_type": detection_type},
            timestamp=datetime.now()
        )

    async def _handle_optimization_request(self, request: AIRequest) -> AIResponse:
        """处理智能优化请求"""
        optimization_type = request.request_data.get("optimization_type", "production_schedule")
        input_data = request.request_data.get("input_data", {})

        # 集成强化学习优化
        if optimization_type == "production_schedule" and self.config["cross_service_integration"]:
            rl_result = await self._use_rl_for_optimization(input_data, request)
            input_data["rl_suggestions"] = rl_result

        if self.intelligent_optimization is None:
            # 使用统一算法核心进行优化
            if optimization_type == "production_schedule":
                # 转换为DataFrame格式
                df_data = pd.DataFrame([input_data]) if isinstance(input_data, dict) else pd.DataFrame(input_data)
                result = self.algorithm_core.predict("production_planning", df_data)
            else:
                result = {
                    "optimized_schedule": ["任务1", "任务2", "任务3"],
                    "efficiency": 0.92,
                    "makespan": 120
                }
        else:
            result = await self.intelligent_optimization.optimize(
                OptimizationType.PRODUCTION_SCHEDULE,
                input_data,
                request.request_data.get("constraints")
            )

        return AIResponse(
            success=True,
            service_type=AIServiceType.INTELLIGENT_OPTIMIZATION,
            data={
                "optimization_result": result,
                "rl_enhanced": "rl_suggestions" in input_data,
                "algorithm_core_used": self.intelligent_optimization is None
            },
            metadata={"optimization_type": optimization_type},
            timestamp=datetime.now()
        )

    async def _handle_rl_request(self, request: AIRequest) -> AIResponse:
        """处理强化学习请求"""
        action_type = request.request_data.get("action_type", "schedule")

        if self.rl_service is None:
            # 简化的强化学习响应
            if action_type == "schedule":
                result = {
                    "optimized_schedule": ["订单1", "订单2", "订单3"],
                    "reward": 0.85,
                    "efficiency": 0.92
                }
            elif action_type == "train":
                result = {
                    "training_completed": True,
                    "episodes": 1000,
                    "final_reward": 0.88
                }
            else:
                result = {"error": f"不支持的强化学习动作: {action_type}"}
        else:
            if action_type == "schedule":
                result = await self.rl_service.optimize_schedule(
                    request.request_data.get("orders", []),
                    request.request_data.get("equipment", {}),
                    request.request_data.get("constraints", {})
                )
            elif action_type == "train":
                result = await self.rl_service.train_model(
                    request.request_data.get("training_data", [])
                )
            else:
                result = {"error": f"不支持的强化学习动作: {action_type}"}

        return AIResponse(
            success="error" not in result,
            service_type=AIServiceType.REINFORCEMENT_LEARNING,
            data=result,
            metadata={"action_type": action_type, "rl_service_available": self.rl_service is not None},
            timestamp=datetime.now()
        )

    async def _handle_learning_request(self, request: AIRequest) -> AIResponse:
        """处理学习引擎请求"""
        learning_type = request.request_data.get("learning_type", "performance_analysis")

        if learning_type == "performance_analysis":
            result = await self._analyze_system_performance()
        elif learning_type == "model_training":
            result = await self._trigger_model_training(request.request_data)
        elif learning_type == "pattern_discovery":
            result = await self._discover_patterns(request.request_data)
        else:
            result = {"error": f"不支持的学习类型: {learning_type}"}

        return AIResponse(
            success="error" not in result,
            service_type=AIServiceType.LEARNING_ENGINE,
            data=result,
            metadata={"learning_type": learning_type},
            timestamp=datetime.now()
        )

    async def get_comprehensive_ai_analysis(self, input_data: Dict[str, Any],
                                          user_id: str) -> Dict[str, Any]:
        """获取综合AI分析 - 整合所有AI服务"""
        try:
            results = {}

            # 1. 预测分析
            prediction_request = AIRequest(
                service_type=AIServiceType.PREDICTIVE_ANALYTICS,
                request_data={
                    "prediction_type": "demand_forecast",
                    "input_data": input_data
                },
                user_id=user_id
            )
            prediction_result = await self.process_request(prediction_request)
            results["predictions"] = prediction_result.data

            # 2. 异常检测
            if "monitoring_data" in input_data:
                anomaly_request = AIRequest(
                    service_type=AIServiceType.ANOMALY_DETECTION,
                    request_data={
                        "detection_type": "ensemble",
                        "data": input_data["monitoring_data"]
                    },
                    user_id=user_id
                )
                anomaly_result = await self.process_request(anomaly_request)
                results["anomalies"] = anomaly_result.data

            # 3. 智能优化
            optimization_request = AIRequest(
                service_type=AIServiceType.INTELLIGENT_OPTIMIZATION,
                request_data={
                    "optimization_type": "production_schedule",
                    "input_data": input_data
                },
                user_id=user_id
            )
            optimization_result = await self.process_request(optimization_request)
            results["optimizations"] = optimization_result.data

            # 4. 强化学习建议
            rl_request = AIRequest(
                service_type=AIServiceType.REINFORCEMENT_LEARNING,
                request_data={
                    "action_type": "schedule",
                    "orders": input_data.get("orders", []),
                    "equipment": input_data.get("equipment", {})
                },
                user_id=user_id
            )
            rl_result = await self.process_request(rl_request)
            results["rl_suggestions"] = rl_result.data

            # 5. 生成综合LLM分析
            llm_request = AIRequest(
                service_type=AIServiceType.LLM_CHAT,
                request_data={
                    "message": f"请基于以下AI分析结果提供综合建议：{results}",
                    "model": "gpt-3.5-turbo",
                    "temperature": 0.7
                },
                user_id=user_id,
                context={"ai_analysis_results": results}
            )
            llm_result = await self.process_request(llm_request)
            results["comprehensive_analysis"] = llm_result.data

            return {
                "success": True,
                "analysis_timestamp": datetime.now().isoformat(),
                "results": results,
                "services_used": [
                    "predictive_analytics",
                    "anomaly_detection",
                    "intelligent_optimization",
                    "reinforcement_learning",
                    "llm_chat"
                ]
            }

        except Exception as e:
            logger.error(f"综合AI分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    # 辅助方法
    async def _build_enhanced_context(self, request: AIRequest) -> Dict[str, Any]:
        """构建增强的上下文信息"""
        context = {}

        # 添加其他AI服务的最新结果
        if hasattr(self, 'recent_predictions'):
            context["recent_predictions"] = self.recent_predictions

        if hasattr(self, 'recent_anomalies'):
            context["recent_anomalies"] = self.recent_anomalies

        return context

    async def _analyze_llm_response_for_ai_actions(self, llm_result: Dict,
                                                 request: AIRequest) -> List[Dict]:
        """分析LLM响应，建议触发其他AI服务"""
        suggestions = []

        response_text = llm_result.get("data", {}).get("response", "").lower()

        if any(keyword in response_text for keyword in ["预测", "forecast", "predict"]):
            suggestions.append({
                "service": "predictive_analytics",
                "reason": "检测到预测需求",
                "suggested_action": "执行需求预测分析"
            })

        if any(keyword in response_text for keyword in ["异常", "anomaly", "问题"]):
            suggestions.append({
                "service": "anomaly_detection",
                "reason": "检测到异常相关讨论",
                "suggested_action": "执行异常检测分析"
            })

        if any(keyword in response_text for keyword in ["优化", "optimize", "改进"]):
            suggestions.append({
                "service": "intelligent_optimization",
                "reason": "检测到优化需求",
                "suggested_action": "执行智能优化分析"
            })

        return suggestions

    # 添加缺失的辅助方法
    async def _enhance_prediction_input(self, input_data: Dict, request: AIRequest) -> Dict:
        """增强预测输入数据"""
        enhanced_input = input_data.copy()

        # 添加时间特征
        enhanced_input['timestamp'] = datetime.now().isoformat()
        enhanced_input['hour'] = datetime.now().hour
        enhanced_input['day_of_week'] = datetime.now().weekday()

        return enhanced_input

    async def _enhance_anomaly_detection(self, df: pd.DataFrame, detection_type: str, request: AIRequest) -> Dict:
        """增强异常检测"""
        return {
            "enhanced": True,
            "detection_type": detection_type,
            "data_shape": df.shape
        }

    async def _use_rl_for_optimization(self, input_data: Dict, request: AIRequest) -> Dict:
        """使用强化学习进行优化"""
        return {
            "rl_suggestions": ["优化建议1", "优化建议2"],
            "confidence": 0.85
        }

    async def _use_learning_engine_prediction(self, prediction_type: str, input_data: Dict) -> Dict:
        """使用学习引擎进行预测"""
        return {
            "predictions": [100, 120, 110],
            "confidence": "medium",
            "prediction_type": prediction_type
        }

    async def _analyze_system_performance(self) -> Dict:
        """分析系统性能"""
        return {
            "overall_performance": 0.85,
            "bottlenecks": ["数据处理", "模型推理"],
            "recommendations": ["增加缓存", "优化算法"]
        }

    async def _trigger_model_training(self, request_data: Dict) -> Dict:
        """触发模型训练"""
        return {
            "training_started": True,
            "estimated_time": "30分钟",
            "model_type": request_data.get("model_type", "unknown")
        }

    async def _discover_patterns(self, request_data: Dict) -> Dict:
        """发现模式"""
        return {
            "patterns_found": 3,
            "patterns": ["模式1", "模式2", "模式3"],
            "confidence": 0.78
        }

    async def _trigger_cross_service_learning(self, request: AIRequest, response: AIResponse):
        """触发跨服务学习"""
        # 记录服务间的交互和学习
        pass

    def _update_performance_metrics(self, service_type: AIServiceType,
                                  processing_time: float, success: bool):
        """更新性能指标"""
        if service_type not in self.performance_metrics:
            self.performance_metrics[service_type] = {
                "total_requests": 0,
                "successful_requests": 0,
                "total_time": 0.0,
                "avg_response_time": 0.0,
                "success_rate": 0.0
            }

        metrics = self.performance_metrics[service_type]
        metrics["total_requests"] += 1
        metrics["total_time"] += processing_time

        if success:
            metrics["successful_requests"] += 1

        metrics["avg_response_time"] = metrics["total_time"] / metrics["total_requests"]
        metrics["success_rate"] = metrics["successful_requests"] / metrics["total_requests"]

    def _record_request(self, request: AIRequest, response: AIResponse,
                       processing_time: float):
        """记录请求历史"""
        self.request_history.append({
            "timestamp": datetime.now(),
            "service_type": request.service_type.value,
            "user_id": request.user_id,
            "success": response.success,
            "processing_time": processing_time
        })

        # 保持最近1000条记录
        if len(self.request_history) > 1000:
            self.request_history = self.request_history[-1000:]

    async def _trigger_cross_service_learning(self, request: AIRequest,
                                            response: AIResponse):
        """触发跨服务学习"""
        # 将请求和响应数据传递给学习引擎
        if response.success and self.learning_engine:
            learning_data = {
                "service_type": request.service_type.value,
                "request_data": request.request_data,
                "response_data": response.data,
                "timestamp": datetime.now()
            }

            # 异步学习，不阻塞主流程
            asyncio.create_task(self._async_learning_update(learning_data))

    async def _async_learning_update(self, learning_data: Dict[str, Any]):
        """异步学习更新"""
        try:
            # 这里可以调用学习引擎的学习方法
            # self.learning_engine.learn_from_interaction(learning_data)
            pass
        except Exception as e:
            logger.error(f"异步学习更新失败: {e}")


# 全局统一AI服务实例
unified_ai_service = UnifiedAIService()
