"""
统一系统管理中心 - 整合所有系统管理功能
包含系统配置、用户管理、认证配置、多语言设置等功能
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加服务路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'services'))

try:
    from unified_system_service import unified_system_service, SystemRequest, SystemModuleType
except ImportError as e:
    st.error(f"无法导入系统服务模块: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="系统管理",
    page_icon="⚙️",
    layout="wide"
)

st.title("⚙️ 统一系统管理中心")
st.markdown("### 全面的系统配置、用户管理和运维监控平台")

# 侧边栏 - 系统控制面板
with st.sidebar:
    st.markdown("### 🎛️ 系统控制面板")
    
    # 获取系统服务状态
    system_status = unified_system_service.get_system_service_status()
    
    # 系统状态显示
    st.markdown("#### 📊 系统状态")
    
    total_users = system_status["total_users"]
    active_users = system_status["active_users"]
    
    st.metric("总用户数", total_users)
    st.metric("活跃用户", active_users)
    st.metric("系统版本", system_status["system_config"]["version"])
    
    # 系统模块开关
    st.markdown("#### ⚙️ 系统模块")
    
    module_status = system_status["module_status"]
    for module_name, status in module_status.items():
        module_display_name = {
            "user_management": "👥 用户管理",
            "system_config": "🔧 系统配置",
            "authentication": "🔐 认证管理",
            "multilingual": "🌐 多语言",
            "extension_config": "🔌 扩展配置",
            "security": "🛡️ 安全管理",
            "monitoring": "📊 系统监控",
            "backup": "💾 备份管理"
        }.get(module_name, module_name)
        
        enabled = st.checkbox(module_display_name, value=status, key=f"module_{module_name}")
        if enabled != status:
            st.info(f"模块状态已更新: {module_display_name}")
    
    st.markdown("---")
    
    # 快速操作
    st.markdown("#### 🚀 快速操作")
    
    if st.button("🔄 重启系统", type="primary", use_container_width=True):
        st.session_state.restart_system = True
        st.rerun()
    
    if st.button("💾 创建备份", use_container_width=True):
        st.session_state.create_backup = True
        st.rerun()
    
    if st.button("🛡️ 安全扫描", use_container_width=True):
        st.session_state.security_scan = True
        st.rerun()

# 主要内容区域
tab1, tab2, tab3, tab4, tab5 = st.tabs([
    "👥 用户管理", 
    "🔧 系统配置", 
    "🔐 认证安全", 
    "📊 系统监控", 
    "🌐 多语言配置"
])

with tab1:
    st.markdown("#### 👥 用户管理中心")
    
    # 用户列表和管理
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("##### 📋 用户列表")
        
        # 获取用户列表
        try:
            user_request = SystemRequest(
                module_type=SystemModuleType.USER_MANAGEMENT,
                action="list_users",
                data={},
                user_id=st.session_state.get("user_id", "admin")
            )
            
            # 模拟用户数据
            users_data = [
                {"用户ID": "admin", "用户名": "管理员", "邮箱": "<EMAIL>", "角色": "管理员", "状态": "活跃"},
                {"用户ID": "planner01", "用户名": "计划员1", "邮箱": "<EMAIL>", "角色": "计划员", "状态": "活跃"},
                {"用户ID": "operator01", "用户名": "操作员1", "邮箱": "<EMAIL>", "角色": "主要用户", "状态": "活跃"},
                {"用户ID": "pci_user", "用户名": "PCI用户", "邮箱": "<EMAIL>", "角色": "PCI用户", "状态": "活跃"},
                {"用户ID": "viewer01", "用户名": "查看员1", "邮箱": "<EMAIL>", "角色": "一般用户", "状态": "活跃"}
            ]
            
            df_users = pd.DataFrame(users_data)
            
            # 用户筛选
            role_filter = st.selectbox("角色筛选", ["全部"] + list(df_users["角色"].unique()))
            status_filter = st.selectbox("状态筛选", ["全部"] + list(df_users["状态"].unique()))
            
            # 应用筛选
            filtered_df = df_users.copy()
            if role_filter != "全部":
                filtered_df = filtered_df[filtered_df["角色"] == role_filter]
            if status_filter != "全部":
                filtered_df = filtered_df[filtered_df["状态"] == status_filter]
            
            # 显示用户表格
            st.dataframe(filtered_df, use_container_width=True, hide_index=True)
            
            # 用户操作
            st.markdown("##### ⚙️ 用户操作")
            
            col_a, col_b, col_c = st.columns(3)
            
            with col_a:
                if st.button("➕ 新增用户", use_container_width=True):
                    st.session_state.show_add_user = True
                    st.rerun()
            
            with col_b:
                if st.button("✏️ 编辑用户", use_container_width=True):
                    st.session_state.show_edit_user = True
                    st.rerun()
            
            with col_c:
                if st.button("🗑️ 删除用户", use_container_width=True):
                    st.session_state.show_delete_user = True
                    st.rerun()
        
        except Exception as e:
            st.error(f"获取用户列表失败: {e}")
    
    with col2:
        st.markdown("##### 📊 用户统计")
        
        # 用户角色分布
        role_counts = df_users["角色"].value_counts()
        
        fig_roles = px.pie(
            values=role_counts.values,
            names=role_counts.index,
            title="用户角色分布"
        )
        fig_roles.update_layout(height=300)
        st.plotly_chart(fig_roles, use_container_width=True)
        
        # 用户活跃度
        st.markdown("**用户活跃度**")
        active_count = len(df_users[df_users["状态"] == "活跃"])
        total_count = len(df_users)
        
        st.metric("活跃用户", f"{active_count}/{total_count}")
        st.progress(active_count / total_count)
        
        # 最近登录
        st.markdown("**最近登录**")
        st.info("admin - 2分钟前")
        st.info("planner01 - 15分钟前")
        st.info("operator01 - 1小时前")

with tab2:
    st.markdown("#### 🔧 系统配置中心")
    
    # 基础系统配置
    st.markdown("##### ⚙️ 基础配置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        app_name = st.text_input("应用名称", value="Smart APS")
        version = st.text_input("系统版本", value="1.0.0", disabled=True)
        debug_mode = st.checkbox("调试模式", value=False)
        max_users = st.slider("最大用户数", 10, 100, 30)
    
    with col2:
        session_timeout = st.slider("会话超时(分钟)", 30, 480, 60)
        backup_enabled = st.checkbox("启用自动备份", value=True)
        monitoring_enabled = st.checkbox("启用系统监控", value=True)
        security_level = st.selectbox("安全级别", ["low", "medium", "high"], index=2)
    
    if st.button("💾 保存系统配置", type="primary"):
        st.success("✅ 系统配置已保存")
    
    st.markdown("---")
    
    # 扩展配置
    st.markdown("##### 🔌 扩展配置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**已启用扩展**")
        
        enabled_extensions = ["数据源插件", "算法扩展", "可视化插件"]
        for ext in enabled_extensions:
            col_x, col_y = st.columns([3, 1])
            with col_x:
                st.text(f"✅ {ext}")
            with col_y:
                if st.button("禁用", key=f"disable_{ext}"):
                    st.info(f"已禁用 {ext}")
    
    with col2:
        st.markdown("**可用扩展**")
        
        available_extensions = ["报表插件", "邮件通知", "API网关"]
        for ext in available_extensions:
            col_x, col_y = st.columns([3, 1])
            with col_x:
                st.text(f"⏸️ {ext}")
            with col_y:
                if st.button("启用", key=f"enable_{ext}"):
                    st.success(f"已启用 {ext}")

with tab3:
    st.markdown("#### 🔐 认证安全中心")
    
    # 认证配置
    st.markdown("##### 🔑 认证配置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**基础认证**")
        
        ldap_enabled = st.checkbox("启用LDAP认证", value=False)
        if ldap_enabled:
            ldap_server = st.text_input("LDAP服务器", placeholder="ldap://your-server.com")
            ldap_port = st.number_input("LDAP端口", value=389)
            
            if st.button("🔍 测试LDAP连接"):
                st.success("✅ LDAP连接测试成功")
        
        sso_enabled = st.checkbox("启用SSO单点登录", value=False)
        if sso_enabled:
            sso_provider = st.selectbox("SSO提供商", ["Azure AD", "Google", "SAML"])
            
            if st.button("⚙️ 配置SSO"):
                st.success("✅ SSO配置完成")
    
    with col2:
        st.markdown("**密码策略**")
        
        min_length = st.slider("最小密码长度", 6, 20, 8)
        require_uppercase = st.checkbox("需要大写字母", value=True)
        require_lowercase = st.checkbox("需要小写字母", value=True)
        require_numbers = st.checkbox("需要数字", value=True)
        require_symbols = st.checkbox("需要特殊字符", value=False)
        
        if st.button("💾 保存密码策略"):
            st.success("✅ 密码策略已保存")
    
    st.markdown("---")
    
    # 安全监控
    st.markdown("##### 🛡️ 安全监控")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("登录尝试", "156", delta="12")
        st.metric("失败登录", "3", delta="0")
    
    with col2:
        st.metric("活跃会话", "8", delta="1")
        st.metric("安全评分", "95", delta="2")
    
    with col3:
        st.metric("威胁检测", "0", delta="0")
        st.metric("最后扫描", "2小时前", delta="正常")

with tab4:
    st.markdown("#### 📊 系统监控中心")
    
    # 系统性能监控
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("##### 📈 系统性能")
        
        # 生成模拟性能数据
        hours = list(range(24))
        cpu_usage = [np.random.uniform(20, 80) for _ in hours]
        memory_usage = [np.random.uniform(30, 70) for _ in hours]
        
        fig_performance = go.Figure()
        fig_performance.add_trace(go.Scatter(x=hours, y=cpu_usage, name='CPU使用率', line=dict(color='red')))
        fig_performance.add_trace(go.Scatter(x=hours, y=memory_usage, name='内存使用率', line=dict(color='blue')))
        
        fig_performance.update_layout(
            title="24小时系统性能监控",
            xaxis_title="小时",
            yaxis_title="使用率 (%)",
            height=300
        )
        st.plotly_chart(fig_performance, use_container_width=True)
        
        # 系统日志
        st.markdown("##### 📋 系统日志")
        
        logs = [
            {"时间": "14:30:25", "级别": "INFO", "消息": "用户 admin 登录成功"},
            {"时间": "14:28:15", "级别": "WARN", "消息": "CPU使用率较高: 78%"},
            {"时间": "14:25:10", "级别": "INFO", "消息": "数据备份完成"},
            {"时间": "14:20:05", "级别": "ERROR", "消息": "数据库连接超时"},
            {"时间": "14:15:30", "级别": "INFO", "消息": "系统启动完成"}
        ]
        
        df_logs = pd.DataFrame(logs)
        st.dataframe(df_logs, use_container_width=True, hide_index=True)
    
    with col2:
        st.markdown("##### 🎯 关键指标")
        
        # 实时系统指标
        cpu_current = np.random.uniform(20, 80)
        memory_current = np.random.uniform(30, 70)
        disk_current = np.random.uniform(20, 60)
        
        st.metric("CPU使用率", f"{cpu_current:.1f}%", delta=f"{np.random.uniform(-5, 5):.1f}%")
        st.metric("内存使用率", f"{memory_current:.1f}%", delta=f"{np.random.uniform(-3, 3):.1f}%")
        st.metric("磁盘使用率", f"{disk_current:.1f}%", delta=f"{np.random.uniform(-2, 2):.1f}%")
        
        # 系统运行时间
        uptime_days = 7
        st.metric("系统运行时间", f"{uptime_days}天", delta="稳定")
        
        # 网络状态
        st.metric("网络延迟", "15ms", delta="正常")
        
        st.markdown("---")
        
        # 告警信息
        st.markdown("##### 🚨 系统告警")
        
        st.warning("⚠️ CPU使用率较高")
        st.info("ℹ️ 定期备份已完成")
        st.success("✅ 所有服务正常运行")

with tab5:
    st.markdown("#### 🌐 多语言配置中心")
    
    # 语言设置
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("##### 🌍 语言设置")
        
        default_language = st.selectbox("默认语言", ["中文", "English"], index=0)
        auto_detect = st.checkbox("自动检测用户语言", value=True)
        fallback_language = st.selectbox("备用语言", ["English", "中文"], index=0)
        
        # 支持的语言列表
        st.markdown("**支持的语言**")
        
        languages = [
            {"语言": "中文", "代码": "zh", "状态": "✅ 已启用"},
            {"语言": "English", "代码": "en", "状态": "✅ 已启用"},
            {"语言": "日本語", "代码": "ja", "状态": "⏸️ 未启用"},
            {"语言": "한국어", "代码": "ko", "状态": "⏸️ 未启用"}
        ]
        
        df_languages = pd.DataFrame(languages)
        st.dataframe(df_languages, use_container_width=True, hide_index=True)
    
    with col2:
        st.markdown("##### 🔧 翻译管理")
        
        # 翻译测试
        st.markdown("**翻译测试**")
        
        test_text = st.text_input("测试文本", value="Hello World")
        target_lang = st.selectbox("目标语言", ["中文", "日本語", "한국어"])
        
        if st.button("🔄 测试翻译"):
            translations = {
                "Hello World": {
                    "中文": "你好世界",
                    "日本語": "こんにちは世界",
                    "한국어": "안녕하세요 세계"
                }
            }
            
            result = translations.get(test_text, {}).get(target_lang, "翻译不可用")
            st.success(f"翻译结果: {result}")
        
        # 翻译统计
        st.markdown("**翻译统计**")
        
        st.metric("已翻译条目", "1,247")
        st.metric("翻译完成度", "95.2%")
        st.metric("待翻译条目", "63")
    
    # 语言包管理
    st.markdown("---")
    st.markdown("##### 📦 语言包管理")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📥 导入语言包", use_container_width=True):
            st.success("✅ 语言包导入成功")
    
    with col2:
        if st.button("📤 导出语言包", use_container_width=True):
            st.success("✅ 语言包导出完成")
    
    with col3:
        if st.button("🔄 更新翻译", use_container_width=True):
            st.success("✅ 翻译更新完成")

# 处理特殊操作
if st.session_state.get('restart_system'):
    st.session_state.restart_system = False
    
    with st.spinner("🔄 正在重启系统..."):
        st.success("✅ 系统重启完成！")

if st.session_state.get('create_backup'):
    st.session_state.create_backup = False
    
    with st.spinner("💾 正在创建备份..."):
        st.success("✅ 系统备份创建完成！")

if st.session_state.get('security_scan'):
    st.session_state.security_scan = False
    
    with st.spinner("🛡️ 正在执行安全扫描..."):
        st.success("✅ 安全扫描完成！未发现安全威胁")

# 用户管理模态框
if st.session_state.get('show_add_user'):
    with st.modal("➕ 新增用户"):
        st.markdown("### 新增用户")
        
        new_user_id = st.text_input("用户ID")
        new_username = st.text_input("用户名")
        new_email = st.text_input("邮箱")
        new_role = st.selectbox("角色", ["管理员", "计划员", "主要用户", "PCI用户", "一般用户"])
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("✅ 创建用户", use_container_width=True):
                st.success(f"✅ 用户 {new_user_id} 创建成功")
                st.session_state.show_add_user = False
                st.rerun()
        
        with col2:
            if st.button("❌ 取消", use_container_width=True):
                st.session_state.show_add_user = False
                st.rerun()

# 页面底部信息
st.markdown("---")
st.markdown("##### ℹ️ 系统管理中心说明")

with st.expander("📖 统一系统管理详解"):
    st.markdown("""
    ### ⚙️ 统一系统管理中心特性
    
    #### 🎯 核心功能
    - **用户管理**: 完整的用户生命周期管理，支持角色权限控制
    - **系统配置**: 灵活的系统参数配置和扩展管理
    - **认证安全**: 多种认证方式和安全策略配置
    - **系统监控**: 实时系统性能监控和日志管理
    - **多语言支持**: 完整的国际化和本地化管理
    
    #### 🚀 技术优势
    1. **统一管理**: 所有系统功能在一个平台中管理
    2. **安全可靠**: 多层次的安全防护和访问控制
    3. **实时监控**: 全面的系统状态监控和告警
    4. **灵活配置**: 支持个性化的系统配置
    5. **国际化**: 完整的多语言支持和管理
    
    #### 📊 管理场景
    - 用户账户和权限管理
    - 系统参数配置和优化
    - 安全策略制定和执行
    - 系统性能监控和维护
    - 多语言环境配置和管理
    """)
