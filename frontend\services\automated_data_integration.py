"""
数据源自动化集成模块
智能识别表格结构、数据格式标准化、增量数据更新
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union
from dataclasses import dataclass
import logging
from datetime import datetime, timedelta
import re
import hashlib
import json
import os
from pathlib import Path
import chardet

@dataclass
class DataSourceInfo:
    """数据源信息"""
    source_type: str  # 'excel', 'csv', 'email', 'database'
    file_path: str
    sheet_name: Optional[str] = None
    encoding: Optional[str] = None
    delimiter: Optional[str] = None
    header_row: int = 0
    data_start_row: int = 1
    columns_mapping: Dict[str, str] = None
    last_modified: Optional[datetime] = None
    file_hash: Optional[str] = None

@dataclass
class TableStructure:
    """表格结构信息"""
    columns: List[str]
    data_types: Dict[str, str]
    sample_data: Dict[str, List]
    row_count: int
    confidence_score: float
    suggested_mapping: Dict[str, str]

@dataclass
class DataIntegrationResult:
    """数据集成结果"""
    success: bool
    data: pd.DataFrame
    source_info: DataSourceInfo
    table_structure: TableStructure
    standardization_log: List[str]
    is_incremental: bool
    new_records_count: int
    error_message: Optional[str] = None

class AutomatedDataIntegrator:
    """自动化数据集成器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.data_cache_dir = "data_cache"
        self.metadata_file = "data_metadata.json"
        self.standard_columns = self._initialize_standard_columns()
        self.data_type_patterns = self._initialize_data_type_patterns()
        
        # 确保缓存目录存在
        os.makedirs(self.data_cache_dir, exist_ok=True)
        
    def _initialize_standard_columns(self) -> Dict[str, List[str]]:
        """初始化标准列名映射"""
        return {
            'demand_forecasting': {
                'date': ['日期', 'date', '时间', 'time', '年月日', 'datetime'],
                'demand': ['需求量', 'demand', '销量', 'sales', '数量', 'quantity', '需求'],
                'product_id': ['产品ID', 'product_id', '产品编号', 'product_code', '商品编号', 'item_id'],
                'price': ['价格', 'price', '单价', 'unit_price', '售价'],
                'category': ['类别', 'category', '分类', '产品类型', 'type']
            },
            'production_planning': {
                'order_id': ['订单号', 'order_id', '工单号', 'wo_number', '订单编号'],
                'quantity': ['数量', 'quantity', '订单量', 'order_qty', '生产量'],
                'due_date': ['交期', 'due_date', '完成日期', 'finish_date', '截止日期'],
                'priority': ['优先级', 'priority', '紧急程度', 'urgency'],
                'processing_time': ['加工时间', 'processing_time', '工时', 'work_hours', '生产时间']
            },
            'quality_prediction': {
                'batch_id': ['批次号', 'batch_id', '批号', 'lot_number'],
                'temperature': ['温度', 'temperature', '温度值'],
                'pressure': ['压力', 'pressure', '压强'],
                'humidity': ['湿度', 'humidity', '相对湿度'],
                'quality_score': ['质量分数', 'quality_score', '质量等级', 'grade']
            },
            'resource_optimization': {
                'resource_id': ['资源ID', 'resource_id', '设备编号', 'equipment_id'],
                'capacity': ['产能', 'capacity', '能力', '最大产量'],
                'utilization': ['利用率', 'utilization', '使用率'],
                'cost': ['成本', 'cost', '费用', '价格']
            }
        }
    
    def _initialize_data_type_patterns(self) -> Dict[str, List[str]]:
        """初始化数据类型识别模式"""
        return {
            'date': [
                r'\d{4}-\d{2}-\d{2}',
                r'\d{2}/\d{2}/\d{4}',
                r'\d{4}/\d{2}/\d{2}',
                r'\d{2}-\d{2}-\d{4}',
                r'\d{4}年\d{1,2}月\d{1,2}日'
            ],
            'numeric': [
                r'^-?\d+\.?\d*$',
                r'^-?\d{1,3}(,\d{3})*\.?\d*$',
                r'^￥?\$?-?\d+\.?\d*$'
            ],
            'id': [
                r'^[A-Z]\d+$',
                r'^\d{6,}$',
                r'^[A-Z]{2,}\d+$'
            ],
            'percentage': [
                r'^\d+\.?\d*%$'
            ]
        }
    
    def integrate_data_source(self, file_path: str, data_type: str = 'general', 
                            force_refresh: bool = False) -> DataIntegrationResult:
        """集成数据源主入口"""
        try:
            self.logger.info(f"开始集成数据源: {file_path}")
            
            # 1. 检测文件类型和基本信息
            source_info = self._detect_source_info(file_path)
            
            # 2. 检查是否需要增量更新
            is_incremental, cached_data = self._check_incremental_update(source_info, force_refresh)
            
            # 3. 智能解析表格结构
            table_structure = self._analyze_table_structure(source_info, data_type)
            
            # 4. 读取和标准化数据
            raw_data = self._read_data_with_structure(source_info, table_structure)
            standardized_data, standardization_log = self._standardize_data_format(raw_data, data_type, table_structure)
            
            # 5. 处理增量更新
            final_data, new_records_count = self._handle_incremental_data(
                standardized_data, cached_data, is_incremental
            )
            
            # 6. 更新缓存和元数据
            self._update_cache_and_metadata(source_info, final_data)
            
            return DataIntegrationResult(
                success=True,
                data=final_data,
                source_info=source_info,
                table_structure=table_structure,
                standardization_log=standardization_log,
                is_incremental=is_incremental,
                new_records_count=new_records_count
            )
            
        except Exception as e:
            self.logger.error(f"数据集成失败: {str(e)}")
            return DataIntegrationResult(
                success=False,
                data=pd.DataFrame(),
                source_info=DataSourceInfo('unknown', file_path),
                table_structure=TableStructure([], {}, {}, 0, 0.0, {}),
                standardization_log=[],
                is_incremental=False,
                new_records_count=0,
                error_message=str(e)
            )
    
    def _detect_source_info(self, file_path: str) -> DataSourceInfo:
        """检测数据源信息"""
        file_path = Path(file_path)
        
        # 检测文件类型
        if file_path.suffix.lower() in ['.xlsx', '.xls']:
            source_type = 'excel'
        elif file_path.suffix.lower() == '.csv':
            source_type = 'csv'
        else:
            raise ValueError(f"不支持的文件类型: {file_path.suffix}")
        
        # 检测编码（对CSV文件）
        encoding = None
        if source_type == 'csv':
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)
                encoding = chardet.detect(raw_data)['encoding']
        
        # 计算文件哈希
        file_hash = self._calculate_file_hash(file_path)
        
        # 获取文件修改时间
        last_modified = datetime.fromtimestamp(file_path.stat().st_mtime)
        
        return DataSourceInfo(
            source_type=source_type,
            file_path=str(file_path),
            encoding=encoding,
            last_modified=last_modified,
            file_hash=file_hash
        )
    
    def _analyze_table_structure(self, source_info: DataSourceInfo, data_type: str) -> TableStructure:
        """智能分析表格结构"""
        # 读取文件头部进行分析
        if source_info.source_type == 'excel':
            # 尝试读取Excel的多个sheet
            excel_file = pd.ExcelFile(source_info.file_path)
            
            best_sheet = None
            best_score = 0
            
            for sheet_name in excel_file.sheet_names:
                try:
                    sample_df = pd.read_excel(source_info.file_path, sheet_name=sheet_name, nrows=10)
                    score = self._score_table_structure(sample_df, data_type)
                    
                    if score > best_score:
                        best_score = score
                        best_sheet = sheet_name
                except:
                    continue
            
            # 使用最佳sheet读取完整数据进行结构分析
            if best_sheet:
                source_info.sheet_name = best_sheet
                df = pd.read_excel(source_info.file_path, sheet_name=best_sheet, nrows=100)
            else:
                df = pd.read_excel(source_info.file_path, nrows=100)
                
        else:  # CSV
            # 尝试不同的分隔符
            delimiters = [',', ';', '\t', '|']
            best_delimiter = ','
            best_score = 0
            
            for delimiter in delimiters:
                try:
                    sample_df = pd.read_csv(
                        source_info.file_path, 
                        delimiter=delimiter, 
                        encoding=source_info.encoding,
                        nrows=10
                    )
                    score = self._score_table_structure(sample_df, data_type)
                    
                    if score > best_score:
                        best_score = score
                        best_delimiter = delimiter
                except:
                    continue
            
            source_info.delimiter = best_delimiter
            df = pd.read_csv(
                source_info.file_path, 
                delimiter=best_delimiter, 
                encoding=source_info.encoding,
                nrows=100
            )
        
        # 分析列结构
        columns = df.columns.tolist()
        data_types = self._detect_column_types(df)
        sample_data = {col: df[col].dropna().head(5).tolist() for col in columns}
        
        # 生成列名映射建议
        suggested_mapping = self._suggest_column_mapping(columns, data_type)
        
        # 计算置信度分数
        confidence_score = self._calculate_structure_confidence(df, data_type, suggested_mapping)
        
        return TableStructure(
            columns=columns,
            data_types=data_types,
            sample_data=sample_data,
            row_count=len(df),
            confidence_score=confidence_score,
            suggested_mapping=suggested_mapping
        )
    
    def _score_table_structure(self, df: pd.DataFrame, data_type: str) -> float:
        """评估表格结构质量分数"""
        if df.empty or len(df.columns) < 2:
            return 0.0
        
        score = 0.0
        
        # 基础分数：列数合理性
        if 3 <= len(df.columns) <= 20:
            score += 30
        
        # 数据完整性
        completeness = (1 - df.isnull().sum().sum() / (df.shape[0] * df.shape[1]))
        score += completeness * 30
        
        # 列名匹配度
        standard_cols = self.standard_columns.get(data_type, {})
        matched_cols = 0
        
        for col in df.columns:
            col_lower = str(col).lower().strip()
            for standard_col, variants in standard_cols.items():
                if any(variant.lower() in col_lower for variant in variants):
                    matched_cols += 1
                    break
        
        if len(df.columns) > 0:
            match_ratio = matched_cols / len(df.columns)
            score += match_ratio * 40
        
        return min(score, 100.0)
    
    def _detect_column_types(self, df: pd.DataFrame) -> Dict[str, str]:
        """检测列的数据类型"""
        column_types = {}
        
        for col in df.columns:
            sample_values = df[col].dropna().astype(str).head(20)
            
            if len(sample_values) == 0:
                column_types[col] = 'unknown'
                continue
            
            # 检测日期类型
            date_count = sum(1 for val in sample_values 
                           if any(re.match(pattern, val) for pattern in self.data_type_patterns['date']))
            
            if date_count / len(sample_values) > 0.7:
                column_types[col] = 'date'
                continue
            
            # 检测数值类型
            numeric_count = sum(1 for val in sample_values 
                              if any(re.match(pattern, val.replace(' ', '')) 
                                   for pattern in self.data_type_patterns['numeric']))
            
            if numeric_count / len(sample_values) > 0.8:
                column_types[col] = 'numeric'
                continue
            
            # 检测ID类型
            id_count = sum(1 for val in sample_values 
                         if any(re.match(pattern, val) for pattern in self.data_type_patterns['id']))
            
            if id_count / len(sample_values) > 0.8:
                column_types[col] = 'id'
                continue
            
            # 检测百分比类型
            percentage_count = sum(1 for val in sample_values 
                                 if any(re.match(pattern, val) for pattern in self.data_type_patterns['percentage']))
            
            if percentage_count / len(sample_values) > 0.7:
                column_types[col] = 'percentage'
                continue
            
            # 默认为文本类型
            column_types[col] = 'text'
        
        return column_types
    
    def _suggest_column_mapping(self, columns: List[str], data_type: str) -> Dict[str, str]:
        """建议列名映射"""
        mapping = {}
        standard_cols = self.standard_columns.get(data_type, {})
        
        for col in columns:
            col_lower = str(col).lower().strip()
            best_match = None
            best_score = 0
            
            for standard_col, variants in standard_cols.items():
                for variant in variants:
                    # 计算相似度分数
                    if variant.lower() == col_lower:
                        score = 100
                    elif variant.lower() in col_lower:
                        score = 80
                    elif col_lower in variant.lower():
                        score = 70
                    else:
                        # 计算编辑距离相似度
                        score = self._calculate_similarity(col_lower, variant.lower()) * 60
                    
                    if score > best_score and score > 50:
                        best_score = score
                        best_match = standard_col
            
            if best_match:
                mapping[col] = best_match
        
        return mapping
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """计算字符串相似度"""
        if not str1 or not str2:
            return 0.0
        
        # 简化的编辑距离算法
        len1, len2 = len(str1), len(str2)
        if len1 > len2:
            str1, str2 = str2, str1
            len1, len2 = len2, len1
        
        distances = list(range(len1 + 1))
        for i2, char2 in enumerate(str2):
            new_distances = [i2 + 1]
            for i1, char1 in enumerate(str1):
                if char1 == char2:
                    new_distances.append(distances[i1])
                else:
                    new_distances.append(1 + min(distances[i1], distances[i1 + 1], new_distances[-1]))
            distances = new_distances
        
        return 1 - distances[-1] / max(len1, len2)
    
    def _calculate_structure_confidence(self, df: pd.DataFrame, data_type: str, mapping: Dict[str, str]) -> float:
        """计算结构识别置信度"""
        if df.empty:
            return 0.0
        
        confidence = 0.0
        
        # 映射覆盖率
        mapping_coverage = len(mapping) / len(df.columns) if len(df.columns) > 0 else 0
        confidence += mapping_coverage * 40
        
        # 数据完整性
        completeness = 1 - (df.isnull().sum().sum() / (df.shape[0] * df.shape[1]))
        confidence += completeness * 30
        
        # 数据类型一致性
        type_consistency = 0
        for col in df.columns:
            col_data = df[col].dropna()
            if len(col_data) > 0:
                # 检查数据类型一致性
                try:
                    if col in mapping:
                        # 根据映射的标准列检查类型一致性
                        type_consistency += 1
                except:
                    pass
        
        if len(df.columns) > 0:
            type_consistency = type_consistency / len(df.columns)
            confidence += type_consistency * 30
        
        return min(confidence, 100.0)
    
    def _read_data_with_structure(self, source_info: DataSourceInfo, structure: TableStructure) -> pd.DataFrame:
        """根据结构信息读取数据"""
        if source_info.source_type == 'excel':
            return pd.read_excel(
                source_info.file_path,
                sheet_name=source_info.sheet_name,
                header=source_info.header_row
            )
        else:  # CSV
            return pd.read_csv(
                source_info.file_path,
                delimiter=source_info.delimiter,
                encoding=source_info.encoding,
                header=source_info.header_row
            )
    
    def _standardize_data_format(self, data: pd.DataFrame, data_type: str, 
                                structure: TableStructure) -> Tuple[pd.DataFrame, List[str]]:
        """标准化数据格式"""
        standardized_data = data.copy()
        log = []
        
        # 应用列名映射
        if structure.suggested_mapping:
            rename_dict = {old_col: new_col for old_col, new_col in structure.suggested_mapping.items() 
                          if old_col in standardized_data.columns}
            if rename_dict:
                standardized_data.rename(columns=rename_dict, inplace=True)
                log.append(f"重命名列: {rename_dict}")
        
        # 标准化数据类型
        for col in standardized_data.columns:
            original_col = None
            for old_col, new_col in structure.suggested_mapping.items():
                if new_col == col:
                    original_col = old_col
                    break
            
            if original_col and original_col in structure.data_types:
                data_type_detected = structure.data_types[original_col]
                
                try:
                    if data_type_detected == 'date':
                        standardized_data[col] = pd.to_datetime(standardized_data[col], errors='coerce')
                        log.append(f"转换 {col} 为日期类型")
                    elif data_type_detected == 'numeric':
                        # 清理数值格式
                        standardized_data[col] = standardized_data[col].astype(str).str.replace(',', '').str.replace('$', '').str.replace('￥', '')
                        standardized_data[col] = pd.to_numeric(standardized_data[col], errors='coerce')
                        log.append(f"转换 {col} 为数值类型")
                    elif data_type_detected == 'percentage':
                        # 处理百分比
                        standardized_data[col] = standardized_data[col].astype(str).str.replace('%', '')
                        standardized_data[col] = pd.to_numeric(standardized_data[col], errors='coerce') / 100
                        log.append(f"转换 {col} 为百分比数值")
                except Exception as e:
                    log.append(f"转换 {col} 失败: {str(e)}")
        
        return standardized_data, log
    
    def _check_incremental_update(self, source_info: DataSourceInfo, force_refresh: bool) -> Tuple[bool, Optional[pd.DataFrame]]:
        """检查是否需要增量更新"""
        if force_refresh:
            return False, None
        
        metadata_path = os.path.join(self.data_cache_dir, self.metadata_file)
        
        if not os.path.exists(metadata_path):
            return False, None
        
        try:
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            file_key = source_info.file_path
            if file_key in metadata:
                cached_info = metadata[file_key]
                
                # 检查文件哈希是否变化
                if cached_info.get('file_hash') == source_info.file_hash:
                    # 文件未变化，加载缓存数据
                    cache_file = os.path.join(self.data_cache_dir, f"{hashlib.md5(file_key.encode()).hexdigest()}.pkl")
                    if os.path.exists(cache_file):
                        cached_data = pd.read_pickle(cache_file)
                        return True, cached_data
            
        except Exception as e:
            self.logger.warning(f"读取缓存元数据失败: {str(e)}")
        
        return False, None
    
    def _handle_incremental_data(self, new_data: pd.DataFrame, cached_data: Optional[pd.DataFrame], 
                                is_incremental: bool) -> Tuple[pd.DataFrame, int]:
        """处理增量数据"""
        if not is_incremental or cached_data is None:
            return new_data, len(new_data)
        
        # 简单的增量逻辑：合并数据并去重
        try:
            combined_data = pd.concat([cached_data, new_data], ignore_index=True)
            combined_data = combined_data.drop_duplicates()
            new_records_count = len(combined_data) - len(cached_data)
            return combined_data, max(0, new_records_count)
        except Exception as e:
            self.logger.warning(f"增量数据处理失败: {str(e)}")
            return new_data, len(new_data)
    
    def _update_cache_and_metadata(self, source_info: DataSourceInfo, data: pd.DataFrame):
        """更新缓存和元数据"""
        try:
            # 保存数据缓存
            file_key = source_info.file_path
            cache_file = os.path.join(self.data_cache_dir, f"{hashlib.md5(file_key.encode()).hexdigest()}.pkl")
            data.to_pickle(cache_file)
            
            # 更新元数据
            metadata_path = os.path.join(self.data_cache_dir, self.metadata_file)
            metadata = {}
            
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
            
            metadata[file_key] = {
                'file_hash': source_info.file_hash,
                'last_modified': source_info.last_modified.isoformat() if source_info.last_modified else None,
                'last_processed': datetime.now().isoformat(),
                'record_count': len(data)
            }
            
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.warning(f"更新缓存失败: {str(e)}")
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的文件格式"""
        return ['.xlsx', '.xls', '.csv']
    
    def clear_cache(self, file_path: str = None):
        """清理缓存"""
        if file_path:
            # 清理特定文件的缓存
            file_key = file_path
            cache_file = os.path.join(self.data_cache_dir, f"{hashlib.md5(file_key.encode()).hexdigest()}.pkl")
            if os.path.exists(cache_file):
                os.remove(cache_file)
        else:
            # 清理所有缓存
            import shutil
            if os.path.exists(self.data_cache_dir):
                shutil.rmtree(self.data_cache_dir)
                os.makedirs(self.data_cache_dir, exist_ok=True)
