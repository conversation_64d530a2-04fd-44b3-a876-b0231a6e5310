"""
数据上传页面
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import io
import time

from config.settings import AppConfig, MESSAGES
from config.theme import apply_plotly_theme
from utils.auth import check_authentication, require_permission
from utils.file_utils import validate_file, preview_file_content
from utils.ui_theme import apply_smart_aps_theme, SmartAPSTheme

# 应用主题
apply_smart_aps_theme()

# 页面配置
st.set_page_config(
    page_title="数据上传 - Smart APS",
    page_icon="📁",
    layout="wide"
)

# 检查认证
if not check_authentication():
    st.error("请先登录")
    st.stop()

# 检查权限
if not require_permission("file.upload"):
    st.error("权限不足")
    st.stop()

# 页面标题 - 带Logo
SmartAPSTheme.create_header_with_logo(
    title="📁 数据上传",
    subtitle="上传Excel、邮件等数据文件，系统将自动解析和提取数据"
)

# 侧边栏 - 上传配置
with st.sidebar:
    st.markdown("### 📋 上传配置")

    auto_process = st.checkbox("自动处理文件", value=True, help="上传后自动解析文件内容")

    st.markdown("### 📊 支持的文件类型")
    file_types = {
        "Excel文件": "📊 .xlsx, .xls",
        "CSV文件": "📄 .csv",
        "邮件文件": "📧 .eml, .msg"
    }

    for file_type, description in file_types.items():
        st.markdown(f"- **{file_type}**: {description}")

    st.markdown("### ⚙️ 处理选项")
    processing_options = st.multiselect(
        "选择处理类型",
        ["表格数据提取", "邮件内容解析", "字段映射配置", "数据验证"],
        default=["表格数据提取"]
    )

# 主要内容区域
tab1, tab2, tab3, tab4 = st.tabs(["📤 文件上传", "📋 上传历史", "🔍 数据预览", "📊 统计分析"])

with tab1:
    st.markdown("#### 📤 文件上传")

    # 文件上传区域
    col1, col2 = st.columns([2, 1])

    with col1:
        uploaded_files = st.file_uploader(
            "选择要上传的文件",
            type=['xlsx', 'xls', 'csv', 'eml', 'msg'],
            accept_multiple_files=True,
            help=f"最大文件大小: {AppConfig.MAX_UPLOAD_SIZE // 1024 // 1024}MB"
        )

        description = st.text_area(
            "文件描述（可选）",
            placeholder="请描述文件内容、来源或用途...",
            height=100
        )

    with col2:
        st.markdown("##### 📝 上传提示")
        st.info("""
        **建议的文件格式：**
        - Excel: 包含订单、库存、设备数据
        - CSV: 结构化数据表
        - 邮件: 包含表格的业务邮件

        **注意事项：**
        - 确保数据格式规范
        - 避免合并单元格
        - 表头信息清晰
        """)

    # 上传按钮和进度
    if uploaded_files:
        st.markdown("---")
        st.markdown("#### 📋 待上传文件列表")

        # 显示文件信息
        for i, file in enumerate(uploaded_files):
            with st.expander(f"📄 {file.name} ({file.size / 1024:.1f} KB)", expanded=True):
                col1, col2, col3 = st.columns([2, 1, 1])

                with col1:
                    st.write(f"**文件类型**: {file.type}")
                    st.write(f"**文件大小**: {file.size / 1024:.1f} KB")

                with col2:
                    # 文件预览
                    if st.button(f"🔍 预览", key=f"preview_{i}"):
                        try:
                            preview_data = preview_file_content(file)
                            if preview_data:
                                st.dataframe(preview_data.head(10), use_container_width=True)
                        except Exception as e:
                            st.error(f"预览失败: {str(e)}")

                with col3:
                    # 验证文件
                    if st.button(f"✅ 验证", key=f"validate_{i}"):
                        validation_result = validate_file(file)
                        if validation_result["valid"]:
                            st.success("文件格式正确")
                        else:
                            st.error(f"文件验证失败: {validation_result['message']}")

        # 批量上传按钮
        st.markdown("---")
        col1, col2, col3 = st.columns([1, 1, 2])

        with col1:
            if st.button("🚀 开始上传", type="primary", use_container_width=True):
                upload_files(uploaded_files, description, auto_process, processing_options)

        with col2:
            if st.button("🗑️ 清空列表", use_container_width=True):
                st.rerun()

with tab2:
    st.markdown("#### 📋 上传历史")

    # 筛选选项
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        status_filter = st.selectbox(
            "状态筛选",
            ["全部", "已上传", "处理中", "已处理", "失败"]
        )

    with col2:
        file_type_filter = st.selectbox(
            "文件类型",
            ["全部", "Excel", "CSV", "邮件"]
        )

    with col3:
        date_range = st.date_input(
            "日期范围",
            value=[datetime.now().date()],
            help="选择查询的日期范围"
        )

    with col4:
        if st.button("🔄 刷新", use_container_width=True):
            st.rerun()

    # 获取上传历史
    upload_history = get_upload_history(status_filter, file_type_filter, date_range)

    if upload_history:
        # 显示统计信息
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("总文件数", len(upload_history))

        with col2:
            processed_count = sum(1 for item in upload_history if item["status"] == "已处理")
            st.metric("已处理", processed_count)

        with col3:
            processing_count = sum(1 for item in upload_history if item["status"] == "处理中")
            st.metric("处理中", processing_count)

        with col4:
            failed_count = sum(1 for item in upload_history if item["status"] == "失败")
            st.metric("失败", failed_count, delta_color="inverse")

        st.markdown("---")

        # 文件列表
        for item in upload_history:
            with st.container():
                col1, col2, col3, col4, col5 = st.columns([3, 1, 1, 1, 1])

                with col1:
                    status_icon = get_status_icon(item["status"])
                    st.write(f"{status_icon} **{item['filename']}**")
                    st.caption(f"上传时间: {item['upload_time']}")

                with col2:
                    st.write(f"{item['file_size']} KB")

                with col3:
                    st.write(item["file_type"])

                with col4:
                    status_color = get_status_color(item["status"])
                    st.markdown(f"<span style='color: {status_color}'>{item['status']}</span>",
                              unsafe_allow_html=True)

                with col5:
                    if st.button("📊 详情", key=f"detail_{item['id']}"):
                        show_file_details(item)

                st.markdown("---")
    else:
        st.info("暂无上传记录")

with tab3:
    st.markdown("#### 🔍 数据预览")

    # 选择要预览的文件
    if 'selected_file_id' not in st.session_state:
        st.session_state.selected_file_id = None

    # 文件选择
    processed_files = get_processed_files()

    if processed_files:
        selected_file = st.selectbox(
            "选择要预览的文件",
            options=processed_files,
            format_func=lambda x: f"{x['filename']} ({x['upload_time']})"
        )

        if selected_file:
            st.session_state.selected_file_id = selected_file['id']

            # 获取提取的数据
            extracted_data = get_extracted_data(selected_file['id'])

            if extracted_data:
                # 数据表格选择
                if len(extracted_data) > 1:
                    selected_table = st.selectbox(
                        "选择数据表",
                        options=extracted_data,
                        format_func=lambda x: f"{x['table_name']} ({x['row_count']}行 x {x['column_count']}列)"
                    )
                else:
                    selected_table = extracted_data[0]

                if selected_table:
                    # 显示数据信息
                    col1, col2, col3, col4 = st.columns(4)

                    with col1:
                        st.metric("数据行数", selected_table['row_count'])

                    with col2:
                        st.metric("数据列数", selected_table['column_count'])

                    with col3:
                        st.metric("数据类型", selected_table['data_type'])

                    with col4:
                        st.metric("导入状态", selected_table['import_status'])

                    # 数据预览
                    st.markdown("##### 📊 数据预览")

                    if selected_table['processed_data']:
                        df = pd.DataFrame(selected_table['processed_data'])

                        # 数据统计
                        with st.expander("📈 数据统计", expanded=False):
                            col1, col2 = st.columns(2)

                            with col1:
                                st.write("**数值列统计**")
                                numeric_cols = df.select_dtypes(include=['number']).columns
                                if len(numeric_cols) > 0:
                                    st.dataframe(df[numeric_cols].describe())
                                else:
                                    st.info("无数值列")

                            with col2:
                                st.write("**文本列统计**")
                                text_cols = df.select_dtypes(include=['object']).columns
                                if len(text_cols) > 0:
                                    for col in text_cols[:5]:  # 只显示前5列
                                        unique_count = df[col].nunique()
                                        st.write(f"**{col}**: {unique_count} 个唯一值")
                                else:
                                    st.info("无文本列")

                        # 数据表格
                        st.dataframe(df, use_container_width=True, height=400)

                        # 字段映射配置
                        if selected_table['import_status'] == 'pending':
                            st.markdown("##### ⚙️ 字段映射配置")
                            configure_field_mapping(selected_table, df.columns.tolist())

                    else:
                        st.warning("数据处理中，请稍后查看")
            else:
                st.info("该文件暂无提取的数据")
    else:
        st.info("暂无已处理的文件")

with tab4:
    st.markdown("#### 📊 统计分析")

    # 获取统计数据
    stats = get_upload_statistics()

    if stats:
        # 总体统计
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric(
                "总文件数",
                stats['total_files'],
                delta=f"+{stats['uploaded_today']} 今日"
            )

        with col2:
            st.metric(
                "处理成功率",
                f"{stats['success_rate']:.1f}%",
                delta=f"{stats['success_rate'] - 85:.1f}%" if stats['success_rate'] >= 85 else None
            )

        with col3:
            st.metric(
                "总存储大小",
                f"{stats['total_size'] / 1024 / 1024:.1f} MB"
            )

        with col4:
            st.metric(
                "平均处理时间",
                f"{stats['avg_processing_time']:.1f}s"
            )

        st.markdown("---")

        # 图表分析
        col1, col2 = st.columns(2)

        with col1:
            # 文件类型分布
            if stats['file_types']:
                fig_types = px.pie(
                    values=list(stats['file_types'].values()),
                    names=list(stats['file_types'].keys()),
                    title="📊 文件类型分布"
                )
                fig_types = apply_plotly_theme(fig_types)
                st.plotly_chart(fig_types, use_container_width=True)

        with col2:
            # 处理状态分布
            status_data = {
                '已处理': stats['processed_files'],
                '处理中': stats['processing_files'],
                '失败': stats['failed_files']
            }

            fig_status = px.bar(
                x=list(status_data.keys()),
                y=list(status_data.values()),
                title="📈 处理状态分布",
                color=list(status_data.keys()),
                color_discrete_map={
                    '已处理': '#2E8B57',
                    '处理中': '#FFD700',
                    '失败': '#DC143C'
                }
            )
            fig_status = apply_plotly_theme(fig_status)
            st.plotly_chart(fig_status, use_container_width=True)

        # 时间趋势
        if stats['daily_uploads']:
            st.markdown("##### 📈 上传趋势")

            dates = [item['date'] for item in stats['daily_uploads']]
            counts = [item['count'] for item in stats['daily_uploads']]

            fig_trend = px.line(
                x=dates,
                y=counts,
                title="📅 每日上传趋势",
                markers=True
            )
            fig_trend = apply_plotly_theme(fig_trend)
            st.plotly_chart(fig_trend, use_container_width=True)

    else:
        st.info("暂无统计数据")


# 辅助函数
def upload_files(files, description, auto_process, processing_options):
    """上传文件"""
    progress_bar = st.progress(0)
    status_text = st.empty()

    api_client = st.session_state.get('api_client')
    if not api_client:
        st.error("API客户端未初始化")
        return

    success_count = 0
    total_files = len(files)

    for i, file in enumerate(files):
        try:
            status_text.text(f"正在上传: {file.name}")

            # 调用API上传文件
            result = api_client.upload_file(
                file_data=file.read(),
                filename=file.name,
                file_type=file.type
            )

            if result.get('success'):
                success_count += 1
                st.success(f"✅ {file.name} 上传成功")
            else:
                st.error(f"❌ {file.name} 上传失败: {result.get('message', '未知错误')}")

            progress_bar.progress((i + 1) / total_files)
            time.sleep(0.5)  # 避免请求过快

        except Exception as e:
            st.error(f"❌ {file.name} 上传失败: {str(e)}")

    status_text.text(f"上传完成: {success_count}/{total_files} 个文件成功")

    if success_count > 0:
        st.balloons()
        time.sleep(2)
        st.rerun()


def get_upload_history(status_filter, file_type_filter, date_range):
    """获取上传历史"""
    # 这里应该调用API获取真实数据
    # 暂时返回模拟数据
    return [
        {
            "id": "1",
            "filename": "订单数据.xlsx",
            "file_size": "156",
            "file_type": "Excel",
            "status": "已处理",
            "upload_time": "2024-01-15 10:30:00"
        },
        {
            "id": "2",
            "filename": "设备清单.csv",
            "file_size": "89",
            "file_type": "CSV",
            "status": "处理中",
            "upload_time": "2024-01-15 11:15:00"
        }
    ]


def get_status_icon(status):
    """获取状态图标"""
    icons = {
        "已上传": "📤",
        "处理中": "⏳",
        "已处理": "✅",
        "失败": "❌"
    }
    return icons.get(status, "📄")


def get_status_color(status):
    """获取状态颜色"""
    colors = {
        "已上传": "#1f77b4",
        "处理中": "#ff7f0e",
        "已处理": "#2ca02c",
        "失败": "#d62728"
    }
    return colors.get(status, "#333333")


def show_file_details(item):
    """显示文件详情"""
    with st.modal(f"文件详情 - {item['filename']}"):
        st.write(f"**文件ID**: {item['id']}")
        st.write(f"**文件名**: {item['filename']}")
        st.write(f"**文件大小**: {item['file_size']} KB")
        st.write(f"**文件类型**: {item['file_type']}")
        st.write(f"**状态**: {item['status']}")
        st.write(f"**上传时间**: {item['upload_time']}")


def get_processed_files():
    """获取已处理的文件列表"""
    # 模拟数据
    return [
        {"id": "1", "filename": "订单数据.xlsx", "upload_time": "2024-01-15 10:30:00"}
    ]


def get_extracted_data(file_id):
    """获取提取的数据"""
    # 模拟数据
    return [
        {
            "id": "1",
            "table_name": "订单表",
            "row_count": 150,
            "column_count": 8,
            "data_type": "order",
            "import_status": "pending",
            "processed_data": [
                {"订单号": "ORD001", "产品": "产品A", "数量": 100, "交期": "2024-01-20"},
                {"订单号": "ORD002", "产品": "产品B", "数量": 200, "交期": "2024-01-22"}
            ]
        }
    ]


def configure_field_mapping(table_data, columns):
    """配置字段映射"""
    st.write("请配置字段映射关系：")

    mapping = {}
    standard_fields = ["订单号", "产品编码", "产品名称", "数量", "单位", "交期", "优先级"]

    for field in standard_fields:
        mapping[field] = st.selectbox(
            f"{field}",
            options=["不映射"] + columns,
            key=f"mapping_{field}"
        )

    if st.button("保存映射配置"):
        # 这里应该调用API保存映射配置
        st.success("字段映射配置已保存")


def get_upload_statistics():
    """获取上传统计"""
    # 模拟数据
    return {
        "total_files": 25,
        "uploaded_today": 3,
        "success_rate": 92.0,
        "total_size": 1024 * 1024 * 50,  # 50MB
        "avg_processing_time": 15.5,
        "processed_files": 20,
        "processing_files": 2,
        "failed_files": 3,
        "file_types": {
            "Excel": 15,
            "CSV": 8,
            "邮件": 2
        },
        "daily_uploads": [
            {"date": "2024-01-10", "count": 5},
            {"date": "2024-01-11", "count": 3},
            {"date": "2024-01-12", "count": 7},
            {"date": "2024-01-13", "count": 4},
            {"date": "2024-01-14", "count": 6}
        ]
    }
