#!/usr/bin/env python3
"""
Smart APS 模块整合验证测试
验证所有统一服务和页面整合是否正常工作
"""

import sys
import os
import importlib.util
from pathlib import Path

def test_unified_services():
    """测试统一服务是否可以正常导入和初始化"""
    print("🔍 测试统一服务...")
    
    services_path = Path("frontend/services")
    
    # 测试统一服务列表
    unified_services = [
        "unified_ai_service.py",
        "unified_algorithm_service.py", 
        "unified_data_service.py",
        "unified_system_service.py"
    ]
    
    results = {}
    
    for service_file in unified_services:
        service_path = services_path / service_file
        service_name = service_file.replace('.py', '')
        
        try:
            # 动态导入服务模块
            spec = importlib.util.spec_from_file_location(service_name, service_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 检查是否有统一服务实例
            if hasattr(module, f"{service_name}"):
                service_instance = getattr(module, f"{service_name}")
                results[service_name] = {"status": "✅ 成功", "instance": True}
            else:
                results[service_name] = {"status": "⚠️ 无实例", "instance": False}
                
        except Exception as e:
            results[service_name] = {"status": f"❌ 失败: {str(e)}", "instance": False}
    
    return results

def test_core_pages():
    """测试核心页面是否存在且结构正确"""
    print("🔍 测试核心页面...")
    
    pages_path = Path("frontend/pages")
    
    # 应该保留的核心页面
    core_pages = [
        "00_综合仪表板.py",
        "01_数据上传.py", 
        "02_生产规划.py",
        "03_设备管理.py",
        "04_计划监控.py",
        "05_数据分析.py",
        "06_智能助手.py",
        "10_PCI管理.py",
        "18_供应链协同.py",
        "19_能耗优化.py",
        "20_算法中心.py",
        "21_数据中心.py",
        "22_系统管理.py"
    ]
    
    # 应该被移除的重复页面
    removed_pages = [
        "07_用户管理.py",
        "08_系统配置.py",
        "09_算法学习中心.py",
        "11_数据集成演示.py",
        "12_AI能力增强.py",
        "12_数据源管理.py",
        "13_算法计划生成.py",
        "14_规划引擎扩展.py",
        "15_多语言测试.py",
        "16_认证配置.py",
        "17_强化学习排程.py"
    ]
    
    results = {
        "core_pages": {},
        "removed_pages": {}
    }
    
    # 检查核心页面是否存在
    for page in core_pages:
        page_path = pages_path / page
        if page_path.exists():
            results["core_pages"][page] = "✅ 存在"
        else:
            results["core_pages"][page] = "❌ 缺失"
    
    # 检查重复页面是否已移除
    for page in removed_pages:
        page_path = pages_path / page
        if not page_path.exists():
            results["removed_pages"][page] = "✅ 已移除"
        else:
            results["removed_pages"][page] = "❌ 仍存在"
    
    return results

def test_page_imports():
    """测试核心页面是否正确导入统一服务"""
    print("🔍 测试页面导入...")
    
    pages_path = Path("frontend/pages")
    
    # 需要检查的页面和对应的统一服务导入
    page_imports = {
        "06_智能助手.py": "unified_ai_service",
        "20_算法中心.py": "unified_algorithm_service", 
        "21_数据中心.py": "unified_data_service",
        "22_系统管理.py": "unified_system_service"
    }
    
    results = {}
    
    for page_file, expected_import in page_imports.items():
        page_path = pages_path / page_file
        
        if not page_path.exists():
            results[page_file] = f"❌ 页面不存在"
            continue
            
        try:
            with open(page_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if expected_import in content:
                results[page_file] = f"✅ 正确导入 {expected_import}"
            else:
                results[page_file] = f"⚠️ 未找到 {expected_import} 导入"
                
        except Exception as e:
            results[page_file] = f"❌ 读取失败: {str(e)}"
    
    return results

def generate_report():
    """生成整合验证报告"""
    print("\n" + "="*60)
    print("🎉 Smart APS 模块整合验证报告")
    print("="*60)
    
    # 测试统一服务
    print("\n📋 1. 统一服务测试")
    print("-" * 30)
    service_results = test_unified_services()
    
    for service, result in service_results.items():
        print(f"{result['status']} {service}")
    
    # 测试核心页面
    print("\n📋 2. 核心页面测试")
    print("-" * 30)
    page_results = test_core_pages()
    
    print("✅ 保留的核心页面:")
    for page, status in page_results["core_pages"].items():
        print(f"  {status} {page}")
    
    print("\n❌ 移除的重复页面:")
    for page, status in page_results["removed_pages"].items():
        print(f"  {status} {page}")
    
    # 测试页面导入
    print("\n📋 3. 页面导入测试")
    print("-" * 30)
    import_results = test_page_imports()
    
    for page, result in import_results.items():
        print(f"{result} - {page}")
    
    # 生成总结
    print("\n📊 整合效果总结")
    print("-" * 30)
    
    # 统计结果
    total_services = len(service_results)
    successful_services = sum(1 for r in service_results.values() if "✅" in r["status"])
    
    total_core_pages = len(page_results["core_pages"])
    existing_core_pages = sum(1 for r in page_results["core_pages"].values() if "✅" in r)
    
    total_removed_pages = len(page_results["removed_pages"])
    actually_removed_pages = sum(1 for r in page_results["removed_pages"].values() if "✅" in r)
    
    total_imports = len(import_results)
    successful_imports = sum(1 for r in import_results.values() if "✅" in r)
    
    print(f"📈 统一服务: {successful_services}/{total_services} 成功")
    print(f"📄 核心页面: {existing_core_pages}/{total_core_pages} 存在")
    print(f"🗑️ 重复页面: {actually_removed_pages}/{total_removed_pages} 已移除")
    print(f"🔗 页面导入: {successful_imports}/{total_imports} 正确")
    
    # 计算总体成功率
    total_tests = total_services + total_core_pages + total_removed_pages + total_imports
    successful_tests = successful_services + existing_core_pages + actually_removed_pages + successful_imports
    success_rate = (successful_tests / total_tests) * 100
    
    print(f"\n🎯 整体成功率: {success_rate:.1f}%")
    
    if success_rate >= 95:
        print("🎉 整合验证完全成功！")
    elif success_rate >= 85:
        print("✅ 整合验证基本成功！")
    else:
        print("⚠️ 整合验证需要改进！")
    
    print("\n" + "="*60)

if __name__ == "__main__":
    generate_report()
