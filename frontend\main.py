"""
Smart APS - 智能工厂生产管理规划系统
Streamlit前端主应用
"""

import streamlit as st
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import AppConfig
from config.theme import apply_custom_theme
from utils.api_client import APIClient
from utils.auth import check_authentication, show_login_page
from utils.ui_theme import apply_smart_aps_theme, SmartAPSTheme

# 页面配置
st.set_page_config(
    page_title="Smart APS - 智能工厂生产管理规划系统",
    page_icon="🏭",
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': 'https://github.com/your-org/smart-aps',
        'Report a bug': 'https://github.com/your-org/smart-aps/issues',
        'About': """
        # Smart APS v1.0.0

        智能工厂生产管理规划系统

        专为20-30人小规模团队设计的生产调度优化平台
        """
    }
)

# 应用Smart APS主题
apply_smart_aps_theme()

# 初始化API客户端
if 'api_client' not in st.session_state:
    st.session_state.api_client = APIClient(AppConfig.API_BASE_URL)


def main():
    """主应用"""
    # 检查用户认证
    if not check_authentication():
        show_login_page()
        return

    # 侧边栏导航
    with st.sidebar:
        st.title("📊 Smart APS")
        st.markdown("---")

        # 用户信息
        user_info = st.session_state.get('user_info', {})
        st.info(f"👋 欢迎，{user_info.get('full_name', '用户')}！")

        # 导航菜单
        st.markdown("### 🧭 导航菜单")

        # 主要功能页面
        if st.button("📊 综合仪表板", use_container_width=True, type="primary"):
            st.switch_page("pages/01_综合仪表板.py")

        st.markdown("---")

        if st.button("📁 数据上传", use_container_width=True):
            st.switch_page("pages/02_数据上传.py")

        if st.button("📋 生产规划", use_container_width=True):
            st.switch_page("pages/03_生产规划.py")

        if st.button("🏭 设备管理", use_container_width=True):
            st.switch_page("pages/04_设备管理.py")

        if st.button("📈 计划监控", use_container_width=True):
            st.switch_page("pages/05_计划监控.py")

        if st.button("📊 数据分析", use_container_width=True):
            st.switch_page("pages/06_数据分析.py")

        if st.button("🤖 智能助手", use_container_width=True):
            st.switch_page("pages/07_智能助手.py")

        # 管理功能（根据权限显示）
        permissions = user_info.get('permissions', [])
        if any(perm.startswith('system.') or perm.startswith('user.') for perm in permissions):
            st.markdown("---")
            st.markdown("### ⚙️ 系统管理")

            if st.button("🔬 PCI管理", use_container_width=True):
                st.switch_page("pages/08_PCI管理.py")

            if st.button("🌐 供应链协同", use_container_width=True):
                st.switch_page("pages/09_供应链协同.py")

            if st.button("⚡ 能耗优化", use_container_width=True):
                st.switch_page("pages/10_能耗优化.py")

            if st.button("🧮 算法中心", use_container_width=True):
                st.switch_page("pages/11_算法中心.py")

            if st.button("📊 数据中心", use_container_width=True):
                st.switch_page("pages/12_数据中心.py")

            if st.button("👥 系统管理", use_container_width=True):
                st.switch_page("pages/13_系统管理.py")

            if st.button("🎯 统一算法管理", use_container_width=True):
                st.switch_page("pages/15_统一算法管理.py")

        st.markdown("---")

        # 退出登录
        if st.button("🚪 退出登录", use_container_width=True, type="secondary"):
            # 清除会话状态
            for key in list(st.session_state.keys()):
                del st.session_state[key]
            st.rerun()

    # 主页面内容 - 带Logo的头部
    SmartAPSTheme.create_header_with_logo(
        title="🏭 Smart APS - 智能工厂生产管理规划系统",
        subtitle="欢迎使用智能工厂生产管理规划系统"
    )

    # 系统概览 - 使用自定义指标卡片
    st.markdown("#### 📊 系统概览")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        SmartAPSTheme.create_metric_card(
            title="📋 今日订单",
            value="25",
            delta="↑ 3 新增",
            delta_color="positive"
        )

    with col2:
        SmartAPSTheme.create_metric_card(
            title="⚙️ 设备利用率",
            value="85%",
            delta="↑ 2% 提升",
            delta_color="positive"
        )

    with col3:
        SmartAPSTheme.create_metric_card(
            title="✅ 计划完成率",
            value="92%",
            delta="↑ 5% 提升",
            delta_color="positive"
        )

    with col4:
        SmartAPSTheme.create_metric_card(
            title="👥 在线用户",
            value="8",
            delta="当前在线",
            delta_color="normal"
        )

    st.markdown("---")

    # 快速操作 - 使用信息卡片
    st.markdown("#### 🚀 快速操作")

    col1, col2 = st.columns(2)

    with col1:
        SmartAPSTheme.create_info_card(
            title="综合仪表板",
            content="查看详细的数据可视化仪表板，实时监控生产状态和关键指标。",
            icon="📊"
        )
        if st.button("进入仪表板", use_container_width=True, type="primary", key="dashboard_btn"):
            st.switch_page("pages/01_综合仪表板.py")

    with col2:
        SmartAPSTheme.create_info_card(
            title="数据上传",
            content="上传Excel、邮件等数据文件，系统将自动解析和处理数据。",
            icon="📁"
        )
        if st.button("上传数据", use_container_width=True, type="primary", key="upload_btn"):
            st.switch_page("pages/02_数据上传.py")

    col3, col4 = st.columns(2)

    with col3:
        SmartAPSTheme.create_info_card(
            title="生产规划",
            content="基于数据生成优化的生产计划，提升生产效率和资源利用率。",
            icon="📋"
        )
        if st.button("生成计划", use_container_width=True, type="primary", key="planning_btn"):
            st.switch_page("pages/03_生产规划.py")

    with col4:
        SmartAPSTheme.create_info_card(
            title="智能助手",
            content="使用AI助手获取专业建议，解答生产管理相关问题。",
            icon="🤖"
        )
        if st.button("咨询助手", use_container_width=True, type="primary", key="ai_btn"):
            st.switch_page("pages/07_智能助手.py")

    st.markdown("---")

    # 最近活动
    st.markdown("#### 📝 最近活动")

    # 这里可以显示最近的系统活动
    activities = [
        {"time": "10:30", "user": "张三", "action": "上传了订单数据文件", "status": "success"},
        {"time": "10:15", "user": "李四", "action": "生成了生产计划", "status": "success"},
        {"time": "09:45", "user": "王五", "action": "更新了设备信息", "status": "info"},
        {"time": "09:30", "user": "赵六", "action": "查看了数据分析报告", "status": "info"},
    ]

    for activity in activities:
        status_icon = "✅" if activity["status"] == "success" else "ℹ️"
        st.markdown(f"{status_icon} **{activity['time']}** - {activity['user']} {activity['action']}")

    st.markdown("---")

    # 系统信息
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("#### 📋 系统信息")
        st.markdown(f"""
        - **版本**: v1.0.0
        - **部署环境**: {'开发环境' if AppConfig.DEBUG else '生产环境'}
        - **API地址**: {AppConfig.API_BASE_URL}
        - **LLM服务**: {'本地Ollama' if AppConfig.DEFAULT_LLM_SERVICE == 'ollama' else 'Azure OpenAI'}
        """)

    with col2:
        st.markdown("#### 🔗 快速链接")
        st.markdown("""
        - [📖 用户手册](https://github.com/your-org/smart-aps/wiki)
        - [🐛 问题反馈](https://github.com/your-org/smart-aps/issues)
        - [💬 技术支持](mailto:<EMAIL>)
        - [🔄 系统更新](https://github.com/your-org/smart-aps/releases)
        """)


if __name__ == "__main__":
    main()
