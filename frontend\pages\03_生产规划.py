"""
生产规划页面
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import json
import time

from config.settings import AppConfig, CHART_CONFIG
from config.theme import apply_plotly_theme
from utils.auth import check_authentication, require_permission
from utils.chart_utils import create_gantt_chart, create_resource_utilization_chart
from services.data_integration_service import data_integration_service
from services.algorithm_planning_service import algorithm_planning_service

# 页面配置
st.set_page_config(
    page_title="生产规划 - Smart APS",
    page_icon="📋",
    layout="wide"
)

# 检查认证
if not check_authentication():
    st.error("请先登录")
    st.stop()

# 检查权限
if not require_permission("plan.view"):
    st.error("权限不足")
    st.stop()

# 页面标题
st.title("📋 生产规划")
st.markdown("### 智能生产计划制定与优化")

# 侧边栏 - 规划配置
with st.sidebar:
    st.markdown("### ⚙️ 规划配置")

    # 优化目标
    optimization_objective = st.selectbox(
        "优化目标",
        ["最小化完工时间", "最大化设备利用率", "最小化延期", "最小化成本", "平衡多目标"],
        help="选择生产规划的主要优化目标"
    )

    # 算法选择
    algorithm = st.selectbox(
        "优化算法",
        ["遗传算法", "模拟退火", "贪心算法", "混合算法"],
        help="选择用于求解的优化算法"
    )

    # 数据驱动选项
    st.markdown("#### 🔗 数据集成")
    use_integrated_data = st.checkbox(
        "使用集成数据",
        value=True,
        help="使用系统集成的设备、PCI、用户输入等数据"
    )

    if use_integrated_data:
        st.success("✅ 将使用实时集成数据")

        # 显示数据源状态
        with st.expander("📊 数据状态", expanded=False):
            try:
                data_context = data_integration_service.get_comprehensive_data_context()
                summary = data_context.get("summary", {})

                col1, col2 = st.columns(2)
                with col1:
                    st.metric("设备数据", f"{summary.get('equipment_status', {}).get('total', 0)}台")
                    st.metric("PCI数据", f"{summary.get('pci_status', {}).get('total_fs_items', 0)}个")
                with col2:
                    st.metric("约束条件", f"{len(data_context.get('constraints', []))}个")
                    st.metric("数据文件", f"{summary.get('data_files', {}).get('recent_files', 0)}个")

            except Exception as e:
                st.error(f"获取数据失败: {str(e)}")
    else:
        st.warning("⚠️ 将使用模拟数据")

    # 算法驱动计划生成
    st.markdown("---")
    if st.button("🧮 算法生成计划", type="primary", use_container_width=True):
        generate_algorithm_plan(optimization_objective, algorithm, use_integrated_data)

    # 约束条件
    st.markdown("#### 约束条件")

    max_overtime = st.slider("最大加班时间(小时/天)", 0, 8, 2)
    setup_time_factor = st.slider("换产时间系数", 0.5, 2.0, 1.0, 0.1)
    quality_threshold = st.slider("质量要求(%)", 80, 100, 95)

    # 时间范围
    st.markdown("#### 规划时间范围")

    plan_start_date = st.date_input(
        "计划开始日期",
        value=datetime.now().date()
    )

    plan_duration = st.selectbox(
        "计划周期",
        ["1周", "2周", "1个月", "3个月"],
        index=1
    )

# 主要内容区域
tab1, tab2, tab3, tab4, tab5 = st.tabs(["📊 计划概览", "🎯 新建计划", "📈 甘特图", "⚙️ 设备配置", "🤖 AI建议"])

with tab1:
    st.markdown("#### 📊 生产计划概览")

    # 获取计划列表
    plans = get_production_plans()

    if plans:
        # 统计信息
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_plans = len(plans)
            st.metric("总计划数", total_plans)

        with col2:
            active_plans = sum(1 for p in plans if p["status"] in ["执行中", "已优化"])
            st.metric("活跃计划", active_plans)

        with col3:
            avg_utilization = sum(p.get("utilization", 0) for p in plans) / len(plans)
            st.metric("平均利用率", f"{avg_utilization:.1f}%")

        with col4:
            on_time_rate = sum(1 for p in plans if p.get("on_time", True)) / len(plans) * 100
            st.metric("按时完成率", f"{on_time_rate:.1f}%")

        st.markdown("---")

        # 计划状态分布
        col1, col2 = st.columns(2)

        with col1:
            status_counts = {}
            for plan in plans:
                status = plan["status"]
                status_counts[status] = status_counts.get(status, 0) + 1

            fig_status = px.pie(
                values=list(status_counts.values()),
                names=list(status_counts.keys()),
                title="📊 计划状态分布"
            )
            fig_status = apply_plotly_theme(fig_status)
            st.plotly_chart(fig_status, use_container_width=True)

        with col2:
            # 设备利用率
            equipment_data = get_equipment_utilization()

            fig_util = px.bar(
                x=[eq["name"] for eq in equipment_data],
                y=[eq["utilization"] for eq in equipment_data],
                title="⚙️ 设备利用率",
                color=[eq["utilization"] for eq in equipment_data],
                color_continuous_scale="RdYlGn"
            )
            fig_util = apply_plotly_theme(fig_util)
            st.plotly_chart(fig_util, use_container_width=True)

        # 计划列表
        st.markdown("#### 📋 计划列表")

        for plan in plans:
            with st.container():
                col1, col2, col3, col4, col5 = st.columns([3, 1, 1, 1, 1])

                with col1:
                    status_icon = get_plan_status_icon(plan["status"])
                    st.write(f"{status_icon} **{plan['name']}**")
                    st.caption(f"创建时间: {plan['created_at']}")

                with col2:
                    st.write(f"{plan['total_orders']} 个订单")

                with col3:
                    st.write(f"{plan['duration']} 天")

                with col4:
                    status_color = get_plan_status_color(plan["status"])
                    st.markdown(f"<span style='color: {status_color}'>{plan['status']}</span>",
                              unsafe_allow_html=True)

                with col5:
                    if st.button("📊 详情", key=f"detail_{plan['id']}"):
                        show_plan_details(plan)

                st.markdown("---")
    else:
        st.info("暂无生产计划，请创建新的计划")

with tab2:
    st.markdown("#### 🎯 新建生产计划")

    with st.form("new_plan_form"):
        col1, col2 = st.columns(2)

        with col1:
            plan_name = st.text_input("计划名称", placeholder="例如：2024年1月生产计划")
            plan_code = st.text_input("计划编码", placeholder="例如：PLAN-2024-01")

            start_date = st.date_input("开始日期", value=plan_start_date)
            end_date = st.date_input("结束日期", value=plan_start_date + timedelta(days=14))

        with col2:
            description = st.text_area("计划描述", height=100)

            # 数据源选择
            data_sources = get_available_data_sources()
            selected_sources = st.multiselect(
                "选择数据源",
                options=data_sources,
                format_func=lambda x: f"{x['name']} ({x['type']})"
            )

        # 高级配置
        with st.expander("🔧 高级配置", expanded=False):
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**优化参数**")
                max_iterations = st.number_input("最大迭代次数", 100, 10000, 1000)
                convergence_tolerance = st.number_input("收敛容差", 0.001, 0.1, 0.01, format="%.3f")
                time_limit = st.number_input("时间限制(分钟)", 1, 60, 30)

            with col2:
                st.markdown("**约束权重**")
                capacity_weight = st.slider("产能约束权重", 0.1, 2.0, 1.0, 0.1)
                deadline_weight = st.slider("交期约束权重", 0.1, 2.0, 1.5, 0.1)
                quality_weight = st.slider("质量约束权重", 0.1, 2.0, 1.2, 0.1)

        # 提交按钮
        submitted = st.form_submit_button("🚀 创建计划", type="primary")

        if submitted:
            if plan_name and plan_code and selected_sources:
                # 记录用户输入数据
                plan_data = {
                    "plan_name": plan_name,
                    "plan_code": plan_code,
                    "description": description,
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "selected_sources": [source["name"] for source in selected_sources],
                    "optimization_objective": optimization_objective,
                    "algorithm": algorithm,
                    "constraints": {
                        "max_overtime": max_overtime,
                        "setup_time_factor": setup_time_factor,
                        "quality_threshold": quality_threshold
                    },
                    "optimization_params": {
                        "max_iterations": max_iterations,
                        "convergence_tolerance": convergence_tolerance,
                        "time_limit": time_limit,
                        "capacity_weight": capacity_weight,
                        "deadline_weight": deadline_weight,
                        "quality_weight": quality_weight
                    }
                }

                # 记录到数据集成服务
                data_integration_service.record_user_input(
                    input_type="production_plan_creation",
                    input_data=plan_data,
                    source_page="02_生产规划",
                    user_id=st.session_state.get("user_id")
                )

                create_production_plan(
                    plan_name, plan_code, description, start_date, end_date,
                    selected_sources, optimization_objective, algorithm,
                    {
                        "max_iterations": max_iterations,
                        "convergence_tolerance": convergence_tolerance,
                        "time_limit": time_limit,
                        "capacity_weight": capacity_weight,
                        "deadline_weight": deadline_weight,
                        "quality_weight": quality_weight
                    }
                )
            else:
                st.error("请填写必要信息并选择数据源")

with tab3:
    st.markdown("#### 📈 甘特图视图")

    # 计划选择
    plans = get_production_plans()
    if plans:
        selected_plan = st.selectbox(
            "选择计划",
            options=plans,
            format_func=lambda x: f"{x['name']} ({x['status']})"
        )

        if selected_plan:
            # 获取甘特图数据
            gantt_data = get_gantt_data(selected_plan["id"])

            if gantt_data:
                # 甘特图控制选项
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    view_mode = st.selectbox("视图模式", ["按设备", "按订单", "按产品"])

                with col2:
                    time_unit = st.selectbox("时间单位", ["小时", "天", "周"])

                with col3:
                    show_critical_path = st.checkbox("显示关键路径", value=True)

                with col4:
                    show_delays = st.checkbox("显示延期", value=True)

                # 图表类型选择
                chart_type = st.selectbox(
                    "图表类型",
                    options=["甘特图", "时间线图", "负载图", "资源分配图", "进度仪表板"],
                    index=0,
                    help="选择生产计划的可视化方式"
                )

                # 根据选择的图表类型显示不同的可视化
                mock_plans = [
                    {
                        "product_name": "产品A",
                        "order_id": "ORD001",
                        "start_time": "2024-01-15 08:00:00",
                        "end_time": "2024-01-17 18:00:00",
                        "equipment_name": "设备1",
                        "quantity": 100,
                        "status": "进行中"
                    },
                    {
                        "product_name": "产品B",
                        "order_id": "ORD002",
                        "start_time": "2024-01-16 08:00:00",
                        "end_time": "2024-01-19 18:00:00",
                        "equipment_name": "设备2",
                        "quantity": 150,
                        "status": "待开始"
                    },
                    {
                        "product_name": "产品C",
                        "order_id": "ORD003",
                        "start_time": "2024-01-14 08:00:00",
                        "end_time": "2024-01-16 18:00:00",
                        "equipment_name": "设备1",
                        "quantity": 80,
                        "status": "已完成"
                    }
                ]

                if chart_type == "甘特图":
                    show_gantt_chart(mock_plans)
                elif chart_type == "时间线图":
                    show_timeline_chart(mock_plans)
                elif chart_type == "负载图":
                    show_workload_chart(mock_plans)
                elif chart_type == "资源分配图":
                    show_resource_allocation_chart(mock_plans)
                elif chart_type == "进度仪表板":
                    show_progress_dashboard(mock_plans)

                # 关键指标
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("总工期", f"{gantt_data['total_duration']} 天")

                with col2:
                    st.metric("关键路径长度", f"{gantt_data['critical_path_length']} 天")

                with col3:
                    st.metric("设备利用率", f"{gantt_data['avg_utilization']:.1f}%")

                with col4:
                    st.metric("按时完成率", f"{gantt_data['on_time_rate']:.1f}%")

                # 资源利用率图表
                st.markdown("##### ⚙️ 资源利用率")

                util_fig = create_resource_utilization_chart(gantt_data["resource_utilization"])
                st.plotly_chart(util_fig, use_container_width=True)

            else:
                st.info("该计划暂无甘特图数据")
    else:
        st.info("暂无可用的生产计划")

with tab4:
    st.markdown("#### ⚙️ 设备配置")

    # 设备列表
    equipment_list = get_equipment_list()

    if equipment_list:
        # 设备统计
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_equipment = len(equipment_list)
            st.metric("设备总数", total_equipment)

        with col2:
            available_count = sum(1 for eq in equipment_list if eq["status"] == "可用")
            st.metric("可用设备", available_count)

        with col3:
            avg_oee = sum(eq.get("oee", 0) for eq in equipment_list) / len(equipment_list)
            st.metric("平均OEE", f"{avg_oee:.1f}%")

        with col4:
            maintenance_count = sum(1 for eq in equipment_list if eq["status"] == "维护中")
            st.metric("维护中", maintenance_count)

        st.markdown("---")

        # 设备配置表格
        equipment_df = pd.DataFrame(equipment_list)

        # 可编辑的设备配置
        edited_df = st.data_editor(
            equipment_df,
            column_config={
                "name": "设备名称",
                "type": "设备类型",
                "capacity": st.column_config.NumberColumn("产能(件/小时)", min_value=0),
                "efficiency": st.column_config.NumberColumn("效率(%)", min_value=0, max_value=100),
                "status": st.column_config.SelectboxColumn(
                    "状态",
                    options=["可用", "忙碌", "维护中", "离线"]
                ),
                "oee": st.column_config.NumberColumn("OEE(%)", min_value=0, max_value=100)
            },
            hide_index=True,
            use_container_width=True
        )

        # 保存配置
        if st.button("💾 保存设备配置"):
            save_equipment_config(edited_df)
            st.success("设备配置已保存")

        # 设备能力矩阵
        st.markdown("##### 🔧 设备能力矩阵")

        capability_matrix = get_equipment_capability_matrix()
        if capability_matrix:
            fig_matrix = px.imshow(
                capability_matrix["matrix"],
                x=capability_matrix["products"],
                y=capability_matrix["equipment"],
                title="设备-产品能力矩阵",
                color_continuous_scale="RdYlGn"
            )
            fig_matrix = apply_plotly_theme(fig_matrix)
            st.plotly_chart(fig_matrix, use_container_width=True)

    else:
        st.info("暂无设备信息")

with tab5:
    st.markdown("#### 🤖 AI智能建议")

    # AI分析选项
    col1, col2 = st.columns(2)

    with col1:
        analysis_type = st.selectbox(
            "分析类型",
            ["瓶颈分析", "效率优化", "成本分析", "质量分析", "综合评估"]
        )

    with col2:
        if st.button("🔍 开始分析", type="primary"):
            with st.spinner("AI正在分析中..."):
                ai_suggestions = get_ai_suggestions(analysis_type)
                st.session_state.ai_suggestions = ai_suggestions

    # 显示AI建议
    if 'ai_suggestions' in st.session_state:
        suggestions = st.session_state.ai_suggestions

        # 总体评估
        st.markdown("##### 📊 总体评估")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric(
                "综合评分",
                f"{suggestions['overall_score']}/100",
                delta=f"{suggestions['score_change']:+.1f}"
            )

        with col2:
            st.metric(
                "改进潜力",
                f"{suggestions['improvement_potential']:.1f}%"
            )

        with col3:
            st.metric(
                "风险等级",
                suggestions['risk_level'],
                delta_color="inverse"
            )

        # 具体建议
        st.markdown("##### 💡 优化建议")

        for i, suggestion in enumerate(suggestions['recommendations']):
            with st.expander(f"{suggestion['priority']} - {suggestion['title']}", expanded=i==0):
                st.write(suggestion['description'])

                col1, col2 = st.columns(2)

                with col1:
                    st.write(f"**预期收益**: {suggestion['expected_benefit']}")
                    st.write(f"**实施难度**: {suggestion['difficulty']}")

                with col2:
                    st.write(f"**影响范围**: {suggestion['impact_scope']}")
                    st.write(f"**实施时间**: {suggestion['implementation_time']}")

                if st.button(f"采纳建议", key=f"adopt_{i}"):
                    adopt_ai_suggestion(suggestion)
                    st.success("建议已采纳，将在下次优化中应用")

        # 趋势预测
        st.markdown("##### 📈 趋势预测")

        prediction_data = suggestions.get('predictions', {})
        if prediction_data:
            fig_prediction = go.Figure()

            fig_prediction.add_trace(go.Scatter(
                x=prediction_data['dates'],
                y=prediction_data['current_trend'],
                mode='lines',
                name='当前趋势',
                line=dict(color='blue')
            ))

            fig_prediction.add_trace(go.Scatter(
                x=prediction_data['dates'],
                y=prediction_data['optimized_trend'],
                mode='lines',
                name='优化后趋势',
                line=dict(color='green', dash='dash')
            ))

            fig_prediction.update_layout(
                title="生产效率趋势预测",
                xaxis_title="日期",
                yaxis_title="效率(%)"
            )

            fig_prediction = apply_plotly_theme(fig_prediction)
            st.plotly_chart(fig_prediction, use_container_width=True)


# 辅助函数
def get_production_plans():
    """获取生产计划列表"""
    # 模拟数据
    return [
        {
            "id": "1",
            "name": "2024年1月生产计划",
            "status": "执行中",
            "created_at": "2024-01-15 10:00:00",
            "total_orders": 25,
            "duration": 14,
            "utilization": 85.5,
            "on_time": True
        },
        {
            "id": "2",
            "name": "紧急订单计划",
            "status": "已优化",
            "created_at": "2024-01-16 14:30:00",
            "total_orders": 8,
            "duration": 5,
            "utilization": 92.3,
            "on_time": True
        }
    ]


def get_equipment_utilization():
    """获取设备利用率数据"""
    return [
        {"name": "设备A", "utilization": 85},
        {"name": "设备B", "utilization": 92},
        {"name": "设备C", "utilization": 78},
        {"name": "设备D", "utilization": 88}
    ]


def get_plan_status_icon(status):
    """获取计划状态图标"""
    icons = {
        "草稿": "📝",
        "优化中": "⚙️",
        "已优化": "✅",
        "执行中": "🚀",
        "已完成": "🏁",
        "已取消": "❌"
    }
    return icons.get(status, "📋")


def get_plan_status_color(status):
    """获取计划状态颜色"""
    colors = {
        "草稿": "#6c757d",
        "优化中": "#fd7e14",
        "已优化": "#20c997",
        "执行中": "#0d6efd",
        "已完成": "#198754",
        "已取消": "#dc3545"
    }
    return colors.get(status, "#333333")


def show_plan_details(plan):
    """显示计划详情"""
    with st.modal(f"计划详情 - {plan['name']}"):
        st.write(f"**计划ID**: {plan['id']}")
        st.write(f"**状态**: {plan['status']}")
        st.write(f"**订单数**: {plan['total_orders']}")
        st.write(f"**工期**: {plan['duration']} 天")
        st.write(f"**利用率**: {plan['utilization']:.1f}%")


def get_available_data_sources():
    """获取可用数据源"""
    return [
        {"id": "1", "name": "订单数据.xlsx", "type": "Excel"},
        {"id": "2", "name": "设备清单.csv", "type": "CSV"}
    ]


def generate_algorithm_plan(optimization_objective, algorithm, use_integrated_data):
    """使用算法生成生产计划"""
    with st.spinner("🧮 算法正在生成生产计划..."):
        try:
            # 映射优化目标
            objective_mapping = {
                "最小化完工时间": "minimize_makespan",
                "最大化设备利用率": "maximize_efficiency",
                "最小化延期": "minimize_makespan",
                "最小化成本": "minimize_cost",
                "平衡多目标": "balanced"
            }

            # 映射算法类型
            algorithm_mapping = {
                "遗传算法": "genetic",
                "模拟退火": "simulated_annealing",
                "贪心算法": "greedy",
                "混合算法": "genetic"
            }

            # 生成计划
            result = algorithm_planning_service.generate_production_plan(
                optimization_objective=objective_mapping.get(optimization_objective, "balanced"),
                time_horizon_days=7,
                algorithm_type=algorithm_mapping.get(algorithm, "genetic"),
                constraints={}
            )

            if result.get("success"):
                st.success("🎉 生产计划生成成功！")

                # 显示计划结果
                show_algorithm_plan_result(result)

                # 记录到数据集成服务
                data_integration_service.record_user_input(
                    input_type="algorithm_plan_generation",
                    input_data={
                        "plan_id": result.get("plan_id"),
                        "algorithm_type": result.get("algorithm_type"),
                        "optimization_objective": result.get("optimization_objective"),
                        "use_integrated_data": use_integrated_data,
                        "generated_at": result.get("generated_at")
                    },
                    source_page="02_生产规划",
                    user_id=st.session_state.get("user_id")
                )

            else:
                st.error(f"❌ 计划生成失败: {result.get('message', '未知错误')}")

        except Exception as e:
            st.error(f"❌ 计划生成异常: {str(e)}")


def show_algorithm_plan_result(result):
    """显示算法生成的计划结果"""
    plan_data = result.get("plan_data", {})
    evaluation = result.get("evaluation", {})

    with st.expander("📋 查看生成的计划", expanded=True):
        # 基本信息
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("计划ID", result.get("plan_id", "")[:8] + "...")
        with col2:
            st.metric("算法类型", result.get("algorithm_type", ""))
        with col3:
            st.metric("优化目标", result.get("optimization_objective", ""))

        # 性能指标
        st.markdown("#### 📊 性能指标")
        metrics = evaluation.get("metrics", {})
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("效率评分", f"{metrics.get('efficiency_score', 0):.1f}")
        with col2:
            st.metric("成本评分", f"{metrics.get('cost_score', 0):.1f}")
        with col3:
            st.metric("交期评分", f"{metrics.get('delivery_score', 0):.1f}")
        with col4:
            st.metric("质量评分", f"{metrics.get('quality_score', 0):.1f}")

        # 设备分配
        st.markdown("#### ⚙️ 设备分配")
        equipment_allocation = plan_data.get("equipment_allocation", {})
        for equipment_id, allocation in equipment_allocation.items():
            st.write(f"**{equipment_id}**: 订单 {', '.join(allocation['orders'])} (利用率: {allocation['utilization']:.1f}%)")

        # 物料消耗
        st.markdown("#### 📦 物料消耗计划")
        material_consumption = plan_data.get("material_consumption", {})
        if material_consumption.get("priority_materials"):
            st.write(f"**优先物料**: {', '.join(material_consumption['priority_materials'])}")
            st.write(f"**消耗策略**: {material_consumption.get('consumption_order', 'FIFO')}")

        # 时间安排
        st.markdown("#### 📅 时间安排")
        timeline = plan_data.get("timeline", {})
        col1, col2 = st.columns(2)
        with col1:
            st.write(f"**开始日期**: {timeline.get('start_date', '')}")
        with col2:
            st.write(f"**结束日期**: {timeline.get('end_date', '')}")

        # 建议
        recommendations = evaluation.get("recommendations", [])
        if recommendations:
            st.markdown("#### 💡 优化建议")
            for rec in recommendations:
                st.info(f"• {rec}")

    # 操作按钮
    col1, col2, col3 = st.columns(3)
    with col1:
        if st.button("✅ 应用计划", key="apply_algorithm_plan"):
            apply_algorithm_plan(result)
    with col2:
        if st.button("📊 更新图表", key="update_algorithm_charts"):
            update_charts_with_algorithm_plan(result)
    with col3:
        if st.button("📤 导出计划", key="export_algorithm_plan"):
            export_algorithm_plan(result)


def apply_algorithm_plan(result):
    """应用算法生成的计划"""
    try:
        with st.spinner("正在应用生产计划..."):
            time.sleep(2)  # 模拟应用过程

        st.success("✅ 算法生成的计划已成功应用到系统！")

        # 记录计划应用
        data_integration_service.record_user_input(
            input_type="algorithm_plan_application",
            input_data={
                "plan_id": result.get("plan_id"),
                "applied_at": datetime.now().isoformat(),
                "applied_by": st.session_state.get("user_id", "anonymous")
            },
            source_page="02_生产规划"
        )

    except Exception as e:
        st.error(f"❌ 应用计划失败: {str(e)}")


def update_charts_with_algorithm_plan(result):
    """使用算法计划更新图表"""
    try:
        with st.spinner("正在更新图表..."):
            time.sleep(1)

        st.success("📊 图表已更新！请查看甘特图标签页")
        st.info("💡 算法生成的计划已应用到甘特图和资源利用率图表")

    except Exception as e:
        st.error(f"❌ 更新图表失败: {str(e)}")


def export_algorithm_plan(result):
    """导出算法生成的计划"""
    try:
        export_data = {
            "plan_info": result,
            "export_time": datetime.now().isoformat(),
            "export_format": "JSON",
            "generated_by": "algorithm"
        }

        st.download_button(
            label="📥 下载算法计划",
            data=json.dumps(export_data, indent=2, ensure_ascii=False),
            file_name=f"algorithm_plan_{result.get('plan_id', 'unknown')}.json",
            mime="application/json"
        )

        st.success("📤 算法计划文件已准备好下载！")

    except Exception as e:
        st.error(f"❌ 导出失败: {str(e)}")


def create_production_plan(name, code, description, start_date, end_date,
                         sources, objective, algorithm, params):
    """创建生产计划"""
    # 这里应该调用API创建计划
    st.success(f"生产计划 '{name}' 创建成功！")
    st.balloons()


def get_gantt_data(plan_id):
    """获取甘特图数据"""
    # 模拟甘特图数据
    return {
        "tasks": [
            {"id": "1", "name": "订单A", "start": "2024-01-15", "end": "2024-01-17", "resource": "设备1"},
            {"id": "2", "name": "订单B", "start": "2024-01-16", "end": "2024-01-19", "resource": "设备2"}
        ],
        "total_duration": 14,
        "critical_path_length": 12,
        "avg_utilization": 85.5,
        "on_time_rate": 92.0,
        "resource_utilization": {
            "设备1": [80, 85, 90, 75, 88],
            "设备2": [92, 88, 85, 90, 87]
        }
    }


def get_equipment_list():
    """获取设备列表"""
    return [
        {"name": "设备A", "type": "加工中心", "capacity": 50, "efficiency": 85, "status": "可用", "oee": 82},
        {"name": "设备B", "type": "数控车床", "capacity": 30, "efficiency": 92, "status": "可用", "oee": 88},
        {"name": "设备C", "type": "铣床", "capacity": 40, "efficiency": 78, "status": "维护中", "oee": 75}
    ]


def save_equipment_config(df):
    """保存设备配置"""
    # 这里应该调用API保存配置
    pass


def get_equipment_capability_matrix():
    """获取设备能力矩阵"""
    return {
        "matrix": [[1, 0.8, 0.6], [0.9, 1, 0.7], [0.5, 0.9, 1]],
        "equipment": ["设备A", "设备B", "设备C"],
        "products": ["产品X", "产品Y", "产品Z"]
    }


def get_ai_suggestions(analysis_type):
    """获取AI建议"""
    # 模拟AI建议数据
    return {
        "overall_score": 78,
        "score_change": 5.2,
        "improvement_potential": 15.3,
        "risk_level": "中等",
        "recommendations": [
            {
                "priority": "🔴 高优先级",
                "title": "优化设备C的维护计划",
                "description": "设备C的维护频率过高，建议调整维护策略，可提升整体效率12%",
                "expected_benefit": "效率提升12%",
                "difficulty": "中等",
                "impact_scope": "全局",
                "implementation_time": "1-2周"
            },
            {
                "priority": "🟡 中优先级",
                "title": "调整订单排序策略",
                "description": "当前排序未充分考虑换产时间，建议采用基于相似度的排序",
                "expected_benefit": "减少换产时间20%",
                "difficulty": "低",
                "impact_scope": "局部",
                "implementation_time": "3-5天"
            }
        ],
        "predictions": {
            "dates": ["2024-01-15", "2024-01-16", "2024-01-17", "2024-01-18", "2024-01-19"],
            "current_trend": [75, 78, 76, 79, 77],
            "optimized_trend": [75, 80, 83, 85, 87]
        }
    }


def adopt_ai_suggestion(suggestion):
    """采纳AI建议"""
    # 这里应该调用API采纳建议
    pass


# 多种图表类型函数
def show_gantt_chart(plans):
    """显示甘特图"""
    st.markdown("##### 📊 甘特图")

    # 创建甘特图数据
    gantt_data = []
    for plan in plans:
        gantt_data.append({
            "Task": f"{plan['product_name']} (订单{plan['order_id']})",
            "Start": plan["start_time"],
            "Finish": plan["end_time"],
            "Resource": plan["equipment_name"],
            "Quantity": plan["quantity"],
            "Status": plan.get("status", "计划中")
        })

    if gantt_data:
        df_gantt = pd.DataFrame(gantt_data)

        # 转换时间格式
        df_gantt['Start'] = pd.to_datetime(df_gantt['Start'])
        df_gantt['Finish'] = pd.to_datetime(df_gantt['Finish'])

        # 创建甘特图
        fig = px.timeline(
            df_gantt,
            x_start="Start",
            x_end="Finish",
            y="Resource",
            color="Status",
            title="生产计划甘特图",
            hover_data=["Quantity", "Task"]
        )

        fig.update_layout(
            height=500,
            xaxis_title="时间",
            yaxis_title="设备资源",
            showlegend=True
        )

        st.plotly_chart(fig, use_container_width=True)

        # 甘特图统计信息
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_tasks = len(gantt_data)
            st.metric("任务总数", total_tasks)

        with col2:
            total_duration = (df_gantt['Finish'].max() - df_gantt['Start'].min()).days
            st.metric("总工期", f"{total_duration} 天")

        with col3:
            unique_resources = df_gantt['Resource'].nunique()
            st.metric("使用设备", unique_resources)

        with col4:
            avg_task_duration = df_gantt.apply(lambda x: (x['Finish'] - x['Start']).days, axis=1).mean()
            st.metric("平均任务时长", f"{avg_task_duration:.1f} 天")


def show_timeline_chart(plans):
    """显示时间线图"""
    st.markdown("##### ⏰ 时间线图")

    # 创建时间线数据
    timeline_data = []
    for i, plan in enumerate(plans):
        timeline_data.append({
            "x": pd.to_datetime(plan["start_time"]),
            "y": i,
            "text": f"{plan['product_name']}<br>订单: {plan['order_id']}<br>数量: {plan['quantity']}",
            "marker_size": plan["quantity"] / 10,
            "equipment": plan["equipment_name"]
        })

    if timeline_data:
        df_timeline = pd.DataFrame(timeline_data)

        fig = px.scatter(
            df_timeline,
            x="x",
            y="y",
            size="marker_size",
            color="equipment",
            hover_data=["text"],
            title="生产计划时间线"
        )

        fig.update_layout(
            height=400,
            xaxis_title="开始时间",
            yaxis_title="任务序号",
            showlegend=True
        )

        st.plotly_chart(fig, use_container_width=True)


def show_workload_chart(plans):
    """显示负载图"""
    st.markdown("##### 📈 设备负载图")

    # 按设备统计负载
    equipment_workload = {}
    for plan in plans:
        equipment = plan["equipment_name"]
        if equipment not in equipment_workload:
            equipment_workload[equipment] = []

        start_time = pd.to_datetime(plan["start_time"])
        end_time = pd.to_datetime(plan["end_time"])
        duration = (end_time - start_time).total_seconds() / 3600  # 转换为小时

        equipment_workload[equipment].append({
            "date": start_time.date(),
            "hours": duration,
            "product": plan["product_name"],
            "quantity": plan["quantity"]
        })

    # 创建负载图
    workload_data = []
    for equipment, tasks in equipment_workload.items():
        daily_hours = {}
        for task in tasks:
            date = task["date"]
            if date not in daily_hours:
                daily_hours[date] = 0
            daily_hours[date] += task["hours"]

        for date, hours in daily_hours.items():
            workload_data.append({
                "设备": equipment,
                "日期": date,
                "工作小时": hours,
                "负载率": min(hours / 8 * 100, 100)  # 假设每天8小时工作
            })

    if workload_data:
        df_workload = pd.DataFrame(workload_data)

        fig = px.bar(
            df_workload,
            x="日期",
            y="负载率",
            color="设备",
            title="设备每日负载率",
            hover_data=["工作小时"]
        )

        # 添加100%负载线
        fig.add_hline(y=100, line_dash="dash", line_color="red",
                     annotation_text="满负载线")

        fig.update_layout(
            height=400,
            yaxis_title="负载率 (%)",
            showlegend=True
        )

        st.plotly_chart(fig, use_container_width=True)

        # 负载统计
        col1, col2, col3 = st.columns(3)

        with col1:
            avg_load = df_workload["负载率"].mean()
            st.metric("平均负载率", f"{avg_load:.1f}%")

        with col2:
            max_load = df_workload["负载率"].max()
            st.metric("最高负载率", f"{max_load:.1f}%")

        with col3:
            overload_days = len(df_workload[df_workload["负载率"] > 100])
            st.metric("超负载天数", overload_days)


def show_resource_allocation_chart(plans):
    """显示资源分配图"""
    st.markdown("##### 🔧 资源分配图")

    # 统计资源分配
    resource_allocation = {}
    total_quantity = sum(plan["quantity"] for plan in plans)

    for plan in plans:
        equipment = plan["equipment_name"]
        if equipment not in resource_allocation:
            resource_allocation[equipment] = {
                "quantity": 0,
                "tasks": 0,
                "products": set()
            }

        resource_allocation[equipment]["quantity"] += plan["quantity"]
        resource_allocation[equipment]["tasks"] += 1
        resource_allocation[equipment]["products"].add(plan["product_name"])

    # 创建饼图
    equipment_names = list(resource_allocation.keys())
    quantities = [resource_allocation[eq]["quantity"] for eq in equipment_names]

    fig_pie = px.pie(
        values=quantities,
        names=equipment_names,
        title="设备产量分配"
    )

    fig_pie.update_layout(height=400)

    col1, col2 = st.columns(2)

    with col1:
        st.plotly_chart(fig_pie, use_container_width=True)

    with col2:
        # 资源分配表格
        allocation_data = []
        for equipment, data in resource_allocation.items():
            allocation_data.append({
                "设备": equipment,
                "任务数": data["tasks"],
                "总产量": data["quantity"],
                "产品种类": len(data["products"]),
                "产量占比": f"{data['quantity']/total_quantity*100:.1f}%"
            })

        df_allocation = pd.DataFrame(allocation_data)
        st.dataframe(df_allocation, use_container_width=True, hide_index=True)


def show_progress_dashboard(plans):
    """显示进度仪表板"""
    st.markdown("##### 📊 生产进度仪表板")

    # 计算进度统计
    total_plans = len(plans)
    completed_plans = sum(1 for plan in plans if plan.get("status") == "已完成")
    in_progress_plans = sum(1 for plan in plans if plan.get("status") == "进行中")
    pending_plans = sum(1 for plan in plans if plan.get("status") == "待开始")

    # 进度指标
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        completion_rate = (completed_plans / total_plans * 100) if total_plans > 0 else 0
        st.metric("完成率", f"{completion_rate:.1f}%",
                 delta=f"{completion_rate - 75:.1f}%" if completion_rate > 75 else None)

    with col2:
        st.metric("已完成", completed_plans, delta=f"+{completed_plans}")

    with col3:
        st.metric("进行中", in_progress_plans)

    with col4:
        st.metric("待开始", pending_plans)

    # 进度环形图
    progress_data = {
        "状态": ["已完成", "进行中", "待开始"],
        "数量": [completed_plans, in_progress_plans, pending_plans],
        "颜色": ["#28a745", "#ffc107", "#6c757d"]
    }

    fig_donut = px.pie(
        values=progress_data["数量"],
        names=progress_data["状态"],
        title="计划执行状态分布",
        hole=0.4,
        color_discrete_sequence=progress_data["颜色"]
    )

    fig_donut.update_layout(height=400)

    col1, col2 = st.columns(2)

    with col1:
        st.plotly_chart(fig_donut, use_container_width=True)

    with col2:
        # 每日进度趋势
        dates = pd.date_range(start="2024-01-15", periods=7, freq="D")
        daily_progress = [65, 70, 75, 78, 82, 85, completion_rate]

        fig_trend = px.line(
            x=dates,
            y=daily_progress,
            title="每日完成率趋势",
            markers=True
        )

        fig_trend.update_layout(
            height=400,
            xaxis_title="日期",
            yaxis_title="完成率 (%)"
        )

        st.plotly_chart(fig_trend, use_container_width=True)

    # 关键指标仪表盘
    st.markdown("##### 🎯 关键指标")

    col1, col2, col3 = st.columns(3)

    with col1:
        # 效率指标
        efficiency = 85.5
        fig_gauge_eff = go.Figure(go.Indicator(
            mode="gauge+number+delta",
            value=efficiency,
            domain={'x': [0, 1], 'y': [0, 1]},
            title={'text': "生产效率 (%)"},
            delta={'reference': 80},
            gauge={
                'axis': {'range': [None, 100]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 60], 'color': "lightgray"},
                    {'range': [60, 80], 'color': "yellow"},
                    {'range': [80, 100], 'color': "green"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))

        fig_gauge_eff.update_layout(height=300)
        st.plotly_chart(fig_gauge_eff, use_container_width=True)

    with col2:
        # 质量指标
        quality = 96.8
        fig_gauge_qual = go.Figure(go.Indicator(
            mode="gauge+number+delta",
            value=quality,
            domain={'x': [0, 1], 'y': [0, 1]},
            title={'text': "质量合格率 (%)"},
            delta={'reference': 95},
            gauge={
                'axis': {'range': [None, 100]},
                'bar': {'color': "darkgreen"},
                'steps': [
                    {'range': [0, 90], 'color': "lightgray"},
                    {'range': [90, 95], 'color': "yellow"},
                    {'range': [95, 100], 'color': "green"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 98
                }
            }
        ))

        fig_gauge_qual.update_layout(height=300)
        st.plotly_chart(fig_gauge_qual, use_container_width=True)

    with col3:
        # 交期指标
        delivery = 92.3
        fig_gauge_del = go.Figure(go.Indicator(
            mode="gauge+number+delta",
            value=delivery,
            domain={'x': [0, 1], 'y': [0, 1]},
            title={'text': "按时交付率 (%)"},
            delta={'reference': 90},
            gauge={
                'axis': {'range': [None, 100]},
                'bar': {'color': "darkorange"},
                'steps': [
                    {'range': [0, 80], 'color': "lightgray"},
                    {'range': [80, 90], 'color': "yellow"},
                    {'range': [90, 100], 'color': "green"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 95
                }
            }
        ))

        fig_gauge_del.update_layout(height=300)
        st.plotly_chart(fig_gauge_del, use_container_width=True)
